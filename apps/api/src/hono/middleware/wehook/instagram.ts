import { createMiddleware } from "hono/factory";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { logErrorToAnalytics } from "../../../analytics-utils";

// Hilfsfunktion Hex To Bytes
function hexToBytes(hex: string): Uint8Array {
  if (hex.length % 2 !== 0) throw new Error("Invalid hex string length.");
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
    if (isNaN(bytes[i / 2])) throw new Error("Invalid hex character found.");
  }
  return bytes;
}

export const webhookVerificationMiddleware = createMiddleware<AppContext>(
  async (c, next) => {
    if (c.req.method === "GET") {
      const mode = c.req.query("hub.mode");
      const token = c.req.query("hub.verify_token");
      const challenge = c.req.query("hub.challenge");
      const verifyToken = c.env.YOUR_VERIFY_TOKEN; // Aus Env/Secrets holen
      if (!verifyToken) {
        console.error("YOUR_VERIFY_TOKEN not set!");
        return c.text("Config Error", 500);
      }
      if (mode === "subscribe" && token === verifyToken && challenge) {
        console.log("Webhook verification successful!");
        return c.text(challenge);
      } else {
        console.error("Webhook verification failed.");
        throw new HTTPException(403, { message: "Forbidden" });
      }
    }
    if (c.req.method === "POST") {
      const signatureHeader = c.req.header("X-Hub-Signature-256");
      if (!signatureHeader) {
        console.error("Missing signature");
        logErrorToAnalytics(
          c.env,
          "WEBHOOK_AUTH_FAIL",
          "Missing X-Hub-Signature-256"
        );
        throw new HTTPException(401, {
          message: "Unauthorized: Missing signature",
        });
      }
      const rawBody = await c.req.raw.clone().text(); // Klonen wichtig!
      try {
        const secret = c.env.INSTAGRAM_APP_SECRET;
        if (!secret) throw new Error("INSTAGRAM_APP_SECRET not configured");
        const encoder = new TextEncoder();
        const key = await crypto.subtle.importKey(
          "raw",
          encoder.encode(secret),
          { name: "HMAC", hash: "SHA-256" },
          false,
          ["verify"]
        );
        const signature = hexToBytes(signatureHeader.replace("sha256=", ""));
        const isValid = await crypto.subtle.verify(
          "HMAC",
          key,
          signature,
          encoder.encode(rawBody)
        );
        if (!isValid) {
          console.error("Invalid signature");
          logErrorToAnalytics(c.env, "WEBHOOK_AUTH_FAIL", "Invalid signature");
          throw new HTTPException(401, {
            message: "Unauthorized: Invalid signature",
          });
        }
        console.log("Webhook signature verified.");
      } catch (error) {
        console.error("Signature verification error:", error);
        logErrorToAnalytics(
          c.env,
          "WEBHOOK_AUTH_ERROR",
          "Error during signature verification",
          { error: String(error) }
        );
        throw new HTTPException(500, { message: "Verification Error" });
      }
    }
    await next();
  }
);
