import { HTTPException } from "hono/http-exception";
import {
  getConnectionDetails,
  getDbClient,
  isConnectionOwnedByOrganization,
} from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { AppContext, SyncQueueMessage } from "../../../types";
import { Context } from "hono";
import { BlankInput } from "hono/types";

const syncPostsHandler = async (
  c: Context<
    AppContext,
    "/manage/connection/:platformConnectionId/sync-posts",
    BlankInput
  >
) => {
  const platformConnectionId = c.req.param("platformConnectionId");
  const loggedInUserId = c.var.user?.userId;
  const organizationId = c.var.organizationId;

  if (!loggedInUserId)
    throw new HTTPException(401, { message: "User authentication required" });

  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  // Authorization: Check if connection belongs to user's organization
  const db = getDbClient(c.env.DB);
  try {
    const isOrgOwner = await isConnectionOwnedByOrganization(
      db,
      platformConnectionId,
      organizationId
    );

    if (!isOrgOwner)
      throw new HTTPException(403, {
        message:
          "Forbidden: This connection does not belong to your organization",
      });

    const connection = await getConnectionDetails(db, platformConnectionId);
    if (!connection || connection.status !== "active")
      throw new HTTPException(400, {
        message: "Connection is not active or not found",
      });
  } catch (e) {
    console.error(
      `Error checking ownership/status for conn ${platformConnectionId}:`,
      e
    );
    logErrorToAnalytics(c.env, "SYNC_OWNERSHIP_CHECK_ERROR", `Failed check`, {
      platformConnectionId,
      userId: loggedInUserId,
      organizationId,
      error: String(e),
    });
    throw new HTTPException(500, { message: "Internal server error" });
  }

  // Enqueue Task
  try {
    const message: SyncQueueMessage = {
      platformConnectionId,
      triggeredByUserId: loggedInUserId,
    };
    await c.env.SYNC_QUEUE.send(message);
    console.log(
      `Enqueued post sync task for connection: ${platformConnectionId}`
    );
    return c.json({ message: "Post synchronization started." }, 202);
  } catch (e) {
    console.error(
      `Failed to enqueue sync task for conn ${platformConnectionId}:`,
      e
    );
    logErrorToAnalytics(c.env, "SYNC_ENQUEUE_ERROR", `Failed queue send`, {
      platformConnectionId,
      userId: loggedInUserId,
      organizationId,
      error: String(e),
    });
    throw new HTTPException(500, {
      message: "Failed to start synchronization",
    });
  }
};

export default syncPostsHandler;
