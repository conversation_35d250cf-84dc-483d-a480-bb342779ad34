import { Context } from "hono";
import { AppContext } from "../../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq } from "drizzle-orm";

export const getAllConnections = async (
  c: Context<AppContext, "/manage/project/:projectId/connections", BlankInput>
) => {
  console.log("ALL CONNECTIONS");
  const page = Math.min(
    Math.max(1, parseInt(c.req.query("page") || "1", 10)),
    1000
  );

  const limit = Math.min(
    Math.max(1, parseInt(c.req.query("limit") || "20", 10)),
    100
  );

  console.log("PAGEE; LIMIT", page, limit);

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const projectId = c.req.param("projectId");

  const db = getDbClient(c.env.DB);
  try {
    const connections = await db
      .select({
        id: schema.platformConnections.id,
        name: schema.platformConnections.name,
        platformAccountName: schema.platformConnections.platformAccountName,
        lastPolledAt: schema.platformConnections.lastPolledAt,
        projectId: schema.platformConnections.projectId,
        platform: schema.platformConnections.platform,
        status: schema.platformConnections.status,
        createdAt: schema.platformConnections.createdAt,
      })
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.platformConnections.projectId, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(limit)
      .offset(page ? limit * (page - 1) : 0)
      .all();

    return c.json(connections);
  } catch (e) {
    console.error(
      `Failed to fetch connections for project ${projectId} in org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_CONNECTIONS_FETCH_ERROR",
      `Failed to fetch project connections`,
      {
        organizationId,
        projectId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch project connections",
    });
  }
};
