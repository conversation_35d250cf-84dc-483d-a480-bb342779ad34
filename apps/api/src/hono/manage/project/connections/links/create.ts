import { Context } from "hono";
import { AppContext } from "../../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../../manage/account/fix";
import { secureLinkId } from "@repo/utils/nanoid";
import { DateTime } from "luxon";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { and, eq } from "drizzle-orm";

export const createLinkValidator = zValidator(
  "json",
  z.object({
    name: z.string(),
    expores: z.date().nullable().optional(),
  })
);

export const createLink = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections/:connectionId/links",
    {
      in: {
        json: {
          name: string;
          expires?: string | null;
        };
      };
      out: {
        json: {
          name: string;
          expires?: string | null;
        };
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");

  const name = validated.name;
  const expires = validated.expires;
  const userId = c.var.user?.userId;

  if (!name || !userId)
    throw new HTTPException(400, { message: "Project name required" });

  const projectId = c.req.param("projectId");
  const connectionId = c.req.param("connectionId");

  const linkToken = secureLinkId();

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.projects.id,
        connectionId: schema.platformConnections.id,
        connectionPlatform: schema.platformConnections.platform,
      })
      .from(schema.projects)
      .innerJoin(
        schema.platformConnections,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId),
          eq(schema.platformConnections.id, connectionId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, {
        message: "Project or Connection not found",
      });
    }

    const platform = valid.connectionPlatform;
    if (!platform) {
      throw new HTTPException(400, {
        message: "Connection has no platform configured",
      });
    }

    const linkData = await db
      .insert(schema.generatedLinks)
      .values({
        platformConnectionId: valid.connectionId,
        name: name,
        createdBy: userId,
        expiresAt: expires
          ? DateTime.fromISO(expires).toJSDate()
          : DateTime.now().plus({ days: 2 }).toJSDate(), // 2 Tage
        token: linkToken,
      })
      .run();

    return c.json({
      link: linkToken,
    });
  } catch (e) {
    console.error(
      `Failed to add link to connection ${connectionId} on project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(c.env, "LINK_ADD_ERROR", `Failed to add link`, {
      connectionId,
      projectId,
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to add link",
    });
  }
};
