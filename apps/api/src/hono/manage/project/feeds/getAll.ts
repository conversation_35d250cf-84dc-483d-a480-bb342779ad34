import { Context } from "hono";
import { AppContext } from "../../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";

export const getAllFeeds = async (
  c: Context<AppContext, "/manage/project/:projectId/feeds", BlankInput>
) => {
  const page = Math.min(
    Math.max(1, parseInt(c.req.query("page") || "1", 10)),
    1000
  );

  const limit = Math.min(
    Math.max(1, parseInt(c.req.query("limit") || "20", 10)),
    100
  );

  console.log("PAGEE; LIMIT", page, limit);

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const projectId = c.req.param("projectId");

  const db = getDbClient(c.env.DB);
  try {
    const connections = await db
      .select({
        id: schema.feeds.id,
        name: schema.feeds.name,
        description: schema.feeds.description,
        status: schema.feeds.status,
        createdAt: schema.feeds.createdAt,
        updatedAt: schema.feeds.updatedAt,
        lastActivity: schema.feeds.lastActivity,
        connectionCount:
          sql<number>`count(${schema.platformConnections.id})`.mapWith(Number),
        connectionActiveCount:
          sql<number>`count(${schema.platformConnections.id}) filter (where ${schema.platformConnections.status} = 'active')`.mapWith(
            Number
          ),
        postCount: sql<number>`count(${schema.posts.mediaId})`.mapWith(Number),
      })
      .from(schema.feeds)
      .innerJoin(
        schema.projects,
        eq(schema.feeds.projectId, schema.projects.id)
      )
      .leftJoin(
        schema.feedConnections,
        eq(schema.feeds.id, schema.feedConnections.feedId)
      )
      .innerJoin(
        schema.platformConnections,
        eq(
          schema.feedConnections.platformConnectionId,
          schema.platformConnections.id
        )
      )
      .leftJoin(
        schema.posts,
        eq(
          schema.feedConnections.platformConnectionId,
          schema.posts.platformConnectionId
        )
      )
      .where(
        and(
          eq(schema.feeds.projectId, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(limit)
      .offset(page ? limit * (page - 1) : 0)
      .all();

    return c.json(connections);
  } catch (e) {
    console.error(
      `Failed to fetch feeds for project ${projectId} in org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_FEEDS_FETCH_ERROR",
      `Failed to fetch project feeds`,
      {
        organizationId,
        projectId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch project feeds",
    });
  }
};
