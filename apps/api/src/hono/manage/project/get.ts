import { Context } from "hono";
import { AppContext } from "../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";

export const getOneProject = async (
  c: Context<AppContext, "/manage/projects/:projectId", BlankInput>
) => {
  const requestedProjectId = c.req.param("projectId");
  if (!requestedProjectId)
    throw new HTTPException(403, {
      message: "Project ID required",
    });

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const db = getDbClient(c.env.DB);
  try {
    const projects = await db.query.projects.findFirst({
      columns: {
        id: true,
        name: true,
        description: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        feeds: {
          columns: {
            id: true,
            name: true,
            description: true,
            status: true,
            createdAt: true,
            updatedAt: true,
            lastActivity: true,
          },
        },
      },
      where: (projectsTable, { eq, and }) => {
        return and(
          eq(projectsTable.organizationId, organizationId),
          eq(projectsTable.id, requestedProjectId)
        );
      },
    });

    return c.json(projects);
  } catch (e) {
    console.error(
      `Failed to fetch project with id ${requestedProjectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_FETCH_ERROR",
      `Failed to fetch project with id ${requestedProjectId} for org ${organizationId}`,
      {
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch projects",
    });
  }
};
