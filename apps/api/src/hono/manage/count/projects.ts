import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { AppContext } from "../../../types";
import { Context } from "hono";
import { BlankInput } from "hono/types";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";

export const getProjectsCount = async (
  c: Context<AppContext, "/manage/count/projects", BlankInput>
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId) {
    throw new HTTPException(403, {
      message: "Organization context required",
    });
  }

  const db = getDbClient(c.env.DB);
  try {
    const data = await db
      .select({ count: sql<number>`count(*)` })
      .from(schema.projects)
      .where(eq(schema.projects.organizationId, organizationId))
      .get();
    const activeData = await db
      .select({ count: sql<number>`count(*)` })
      .from(schema.projects)
      .where(
        and(
          eq(schema.projects.status, "active"),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();
    return c.json({
      projects: data?.count ?? 0,
      activeProjects: activeData?.count ?? 0,
    });
  } catch (e) {
    console.error(
      `Failed to fetch projects count for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_FETCH_ERROR",
      `Failed to fetch projects`,
      {
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch projects",
    });
  }
};
export default getProjectsCount;
