// src/debouncer-do.ts
import { eq } from "drizzle-orm";
import * as schema from "./db/schema";
import {
  getDbClient,
  getConnectionDetails,
  updateConnectionStatus,
  upsertPost,
} from "./database-service";
import {
  fetchMediaDataAndFirstComments,
  fetchBatchMediaDataAndFirstComments,
  GraphApiError,
} from "./graph-api";
import { decryptToken } from "./token-utils";
import { logErrorToAnalytics } from "./analytics-utils";
import {
  Bindings,
  WebhookSignalData,
  PaginationQueueMessage,
  MediaCooldownState,
  InsertPost,
  PlatformConnection,
  ApiFetchResult,
  BatchApiResultItem,
} from "./types";

// Konstanten
const IDLE_ALARM_INTERVAL_MS = 15 * 1000;
const BUSY_ALARM_INTERVAL_MS = 60 * 1000;
const COOLDOWN_DURATION_MS = 60 * 1000;
const GRAPH_API_BATCH_LIMIT = 50;

export class DebouncerDO implements DurableObject {
  state: DurableObjectState;
  env: Bindings;
  db: ReturnType<typeof getDbClient>;
  platformConnectionId: string;

  constructor(state: DurableObjectState, env: Bindings) {
    this.state = state;
    this.env = env;
    this.db = getDbClient(env.DB);
    // Annahme: idFromName(platformConnectionId) wurde verwendet, ID = Name
    this.platformConnectionId = state.id.toString();
    // Stelle sicher, dass der Alarm initial läuft (asynchron im Hintergrund)
    this.state.waitUntil(this.ensureAlarm(IDLE_ALARM_INTERVAL_MS));
    console.log(`DO Instance ${this.platformConnectionId} constructed.`);
  }

  /** Stellt sicher, dass ein Alarm geplant ist, wenn keiner existiert oder der nächste bald fällig ist */
  async ensureAlarm(delayMs: number): Promise<void> {
    try {
      const currentAlarm = await this.state.storage.getAlarm();
      if (currentAlarm === null || currentAlarm < Date.now() + 5000) {
        console.log(
          `DO ${this.platformConnectionId}: Setting/Resetting alarm with delay ${delayMs / 1000}s.`
        );
        await this.state.storage.setAlarm(Date.now() + delayMs);
      }
    } catch (e) {
      console.error(
        `DO ${this.platformConnectionId}: Failed to ensure alarm:`,
        e
      );
      this.logErrorToDoAnalytics(
        "DO_ENSURE_ALARM_FAIL",
        `Failed to ensure alarm`,
        { platformConnectionId: this.platformConnectionId, error: String(e) }
      );
    }
  }

  /** Empfängt Signale vom Hauptworker */
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    if (url.pathname === "/signal" && request.method === "POST") {
      try {
        const data =
          await request.json<Omit<WebhookSignalData, "platformConnectionId">>();
        await this.handleWebhookSignalWithCooldown(
          data.mediaId,
          data.verb,
          data.commentId
        );
        return new Response("Signal received", { status: 200 });
      } catch (e) {
        console.error(
          `DO ${this.platformConnectionId}: Error processing signal:`,
          e
        );
        this.logErrorToDoAnalytics(
          "DO_SIGNAL_ERROR",
          "Error processing signal",
          { error: String(e) }
        );
        return new Response("Bad Request", { status: 400 });
      }
    }
    return new Response("Not found", { status: 404 });
  }

  /** Verarbeitet Webhook-Signale mit Cooldown-Logik */
  async handleWebhookSignalWithCooldown(
    mediaId: string,
    verb?: WebhookSignalData["verb"],
    commentId?: string
  ): Promise<void> {
    console.log(
      `DO ${this.platformConnectionId}: Handling Cooldown signal for mediaId=${mediaId} (Verb: ${verb}, CommentId: ${commentId})`
    );
    const stateKey = `cooldown:${mediaId}`;
    let currentState: MediaCooldownState | undefined;
    try {
      currentState = await this.state.storage.get<MediaCooldownState>(stateKey);
    } catch (e) {
      /* Log state read error */
    }
    const now = Date.now();
    const isInCooldown =
      currentState?.cooldownUntil && now < currentState.cooldownUntil;

    if (!isInCooldown) {
      console.log(
        `DO ${this.platformConnectionId}: Media ${mediaId} not in cooldown. Processing immediately.`
      );
      this.state.waitUntil(this.processMediaUpdate(mediaId, verb)); // Wichtig: state.waitUntil!
      const newState: MediaCooldownState = {
        cooldownUntil: now + COOLDOWN_DURATION_MS,
        needsFetchAfterCooldown: false,
      };
      try {
        await this.state.storage.put(stateKey, newState);
      } catch (e) {
        /* Log state write error */
        console.error(
          `DO ${this.platformConnectionId}: Error writing cooldown state for ${mediaId}:`,
          e
        );
      }
      console.log(
        `DO ${this.platformConnectionId}: Cooldown set for ${mediaId} until ${new Date(newState.cooldownUntil!).toISOString()}`
      );
    } else {
      console.log(
        `DO ${this.platformConnectionId}: Media ${mediaId} is in cooldown. Marking for later fetch.`
      );
      const newState: MediaCooldownState = {
        ...currentState,
        needsFetchAfterCooldown: true,
      };
      try {
        await this.state.storage.put(stateKey, newState);
      } catch (e) {
        /* Log state write error */
      }
    }
    await this.ensureAlarm(IDLE_ALARM_INTERVAL_MS);
  }

  /** Verarbeitet das Update für ein einzelnes Medium (API Call, DB Write) */
  async processMediaUpdate(
    mediaId: string,
    initialVerb?: WebhookSignalData["verb"]
  ): Promise<boolean> {
    console.log(
      `DO ${this.platformConnectionId}: Starting processing for media ${mediaId}`
    );
    let accessToken: string | null = null;
    let success = false;
    try {
      // 1. Lade Connection & Token
      const connection = await getConnectionDetails(
        this.db,
        this.platformConnectionId
      );
      if (
        !connection ||
        connection.status !== "active" ||
        !connection.accessTokenEncrypted
      ) {
        console.warn(
          `DO ${this.platformConnectionId}: Conn ${this.platformConnectionId} not active/no token. Aborting processing for ${mediaId}.`
        );
        return false;
      }
      accessToken = await decryptToken(
        connection.accessTokenEncrypted,
        this.env
      );
      if (!accessToken) {
        await this.markConnectionForReauth(this.platformConnectionId);
        this.logErrorToDoAnalytics(
          "DO_PROCESS_DECRYPT_FAIL",
          "Failed decrypt token",
          { mediaId }
        );
        return false;
      }

      // 2. Rufe API Daten ab (nutzt Einzel-Fetch)
      const apiResult = await fetchMediaDataAndFirstComments(
        mediaId,
        accessToken,
        this.env
      ); // Env für Logging übergeben

      if (apiResult && apiResult.baseData) {
        // 3. Schreibe Post Daten (nutzt DB Service)
        const postData: InsertPost = {
          id: crypto.randomUUID(), // Generiere eigene ID
          platformConnectionId: this.platformConnectionId,
          mediaId: apiResult.baseData.id || mediaId, // Nimm ID aus API wenn vorhanden
          likeCount: apiResult.baseData.like_count ?? 0,
          commentsCount: apiResult.baseData.comments_count ?? 0,
          caption: apiResult.baseData.caption,
          mediaUrl: apiResult.baseData.media_url,
          mediaType: apiResult.baseData.media_type ?? "UNKNOWN",
          permalink: apiResult.baseData.permalink,
          timestamp: apiResult.baseData.timestamp
            ? new Date(apiResult.baseData.timestamp)
            : new Date(),
          lastWebhookUpdate: initialVerb ? new Date() : undefined, // Nur bei direkter Verarbeitung durch Webhook setzen
          lastFetched: new Date(),
        };
        await upsertPost(this.db, postData);

        // 4. Skip comment processing - comments are not stored
        console.log(
          `DO ${this.platformConnectionId}: Skipping comment processing for media ${mediaId} (comments not stored)`
        );

        // 5. Enqueue Paginierung falls nötig
        if (apiResult.nextPageUrl) {
          const queueMessage: PaginationQueueMessage = {
            platformConnectionId: this.platformConnectionId,
            mediaId,
            nextPageUrl: apiResult.nextPageUrl,
          };
          try {
            await this.env.PAGINATION_QUEUE.send(queueMessage);
          } catch (e) {
            console.error(
              `DO: Error sending to pagination queue for ${mediaId}`,
              e
            );
            this.logErrorToDoAnalytics(
              "DO_PAGINATION_ENQUEUE_ERROR",
              "Error sending to queue",
              { mediaId, error: String(e) }
            );
          }
        }
        console.log(
          `DO ${this.platformConnectionId}: Successfully processed media ${mediaId}`
        );
        success = true;
      } else {
        console.log(
          `DO ${this.platformConnectionId}: API fetch failed for media ${mediaId}.`
        );
        // Fehler wurde bereits im API Client geloggt. Ggf. Status der Connection prüfen.
        // Könnte ein Auth-Fehler gewesen sein.
        const connectionCheck = await getConnectionDetails(
          this.db,
          this.platformConnectionId
        );
        if (connectionCheck?.status === "auth_needed") {
          console.warn(
            `DO ${this.platformConnectionId}: Connection for media ${mediaId} requires re-authentication.`
          );
        }
      }
    } catch (error: any) {
      console.error(
        `DO ${this.platformConnectionId}: CRITICAL Error during processing media ${mediaId}:`,
        error
      );
      this.logErrorToDoAnalytics(
        "DO_PROCESS_ITEM_ERROR",
        `Error processing media ${mediaId}`,
        { mediaId, error: String(error) }
      );
      // Prüfe auf Auth Fehler vom API Client und markiere Connection
      if (error instanceof GraphApiError && error.isAuthError) {
        await this.markConnectionForReauth(this.platformConnectionId);
      }
      success = false;
    }
    return success;
  }

  /** Der adaptive Alarm-Handler, nutzt Batch API Calls */
  async alarm(): Promise<void> {
    console.log(`DO ${this.platformConnectionId}: Adaptive alarm triggered.`);
    let workDone = false;
    let nextAlarmDelay = IDLE_ALARM_INTERVAL_MS; // Standard: 15 Sekunden

    let mediaToFetchAfterCooldown: string[] = [];
    const now = Date.now();

    // 1. Finde fällige Media IDs
    try {
      const storedStates = await this.state.storage.list<MediaCooldownState>({
        prefix: "cooldown:",
        limit: 500,
      }); // Limit gegen zu viele States
      let stateDeletePromises: Promise<boolean>[] = [];
      for (const [key, state] of storedStates) {
        if (state.cooldownUntil && now >= state.cooldownUntil) {
          if (state.needsFetchAfterCooldown) {
            mediaToFetchAfterCooldown.push(key.replace("cooldown:", ""));
          }
          stateDeletePromises.push(this.state.storage.delete(key));
        }
      }
      if (storedStates.size >= 500) {
        console.warn(
          `DO ${this.platformConnectionId}: Found 500+ cooldown states, list() might be truncated.`
        );
        this.logErrorToDoAnalytics(
          "DO_ALARM_LIST_TRUNCATED",
          "Cooldown state list might be truncated",
          { count: storedStates.size }
        );
      }
      await Promise.all(stateDeletePromises);
    } catch (e) {
      console.error(
        `DO ${this.platformConnectionId}: Error listing/deleting cooldown states:`,
        e
      );
      this.logErrorToDoAnalytics(
        "DO_ALARM_LIST_ERROR",
        "Error listing cooldown states",
        { error: String(e) }
      );
      // Wichtig: Nächsten Alarm trotzdem planen!
      this.state.waitUntil(
        this.state.storage.setAlarm(Date.now() + nextAlarmDelay)
      );
      return;
    }

    // 2. Wenn Arbeit anfällt, verarbeiten
    if (mediaToFetchAfterCooldown.length > 0) {
      console.log(
        `DO ${this.platformConnectionId}: Found ${mediaToFetchAfterCooldown.length} items for post-cooldown fetch.`
      );

      // 3. Token holen (nur einmal pro Alarm)
      let accessToken: string | null = null;
      try {
        const connection = await getConnectionDetails(
          this.db,
          this.platformConnectionId
        );
        if (
          !connection ||
          connection.status !== "active" ||
          !connection.accessTokenEncrypted
        ) {
          throw new Error("Connection inactive or no token");
        }
        accessToken = await decryptToken(
          connection.accessTokenEncrypted,
          this.env
        );
        if (!accessToken) {
          await this.markConnectionForReauth(this.platformConnectionId);
          throw new Error("Token decryption failed");
        }
      } catch (tokenError: any) {
        console.error(
          `DO ${this.platformConnectionId}: Could not get valid token for post-cooldown processing:`,
          tokenError
        );
        this.logErrorToDoAnalytics(
          "DO_ALARM_TOKEN_ERROR",
          "Failed getting token in alarm",
          { error: String(tokenError) }
        );
        // Nächsten Alarm planen und beenden
        this.state.waitUntil(
          this.state.storage.setAlarm(Date.now() + nextAlarmDelay)
        );
        return;
      }

      // 4. Führe Batch API Calls aus
      let successfulProcessingCount = 0;
      for (
        let i = 0;
        i < mediaToFetchAfterCooldown.length;
        i += GRAPH_API_BATCH_LIMIT
      ) {
        const batchMediaIds = mediaToFetchAfterCooldown.slice(
          i,
          i + GRAPH_API_BATCH_LIMIT
        );
        console.log(
          `DO ${this.platformConnectionId}: Processing batch ${i / GRAPH_API_BATCH_LIMIT + 1} with ${batchMediaIds.length} items.`
        );
        try {
          // Nutze die Batch-Funktion aus dem API Client
          const batchResults = await fetchBatchMediaDataAndFirstComments(
            batchMediaIds,
            accessToken,
            this.env
          ); // Env für Logging

          if (batchResults) {
            let writePromises: Promise<void>[] = [];
            let queuePromises: Promise<void>[] = [];
            for (const result of batchResults) {
              // result ist BatchApiResultItem
              if (result.success && result.data?.baseData) {
                successfulProcessingCount++;
                const mediaId = result.mediaId;
                const apiData = result.data; // ApiFetchResult

                if (!apiData.baseData) {
                  console.error(`DO: No baseData for ${mediaId}`);
                  continue;
                }

                // Schreibe Post Daten
                const postData: InsertPost = {
                  id: crypto.randomUUID(),
                  platformConnectionId: this.platformConnectionId,
                  mediaId: apiData.baseData.id,
                  likeCount: apiData.baseData?.like_count ?? 0,
                  commentsCount: apiData.baseData.comments_count ?? 0,
                  caption: apiData.baseData?.caption,
                  mediaUrl: apiData.baseData.media_url,
                  mediaType: apiData.baseData.media_type ?? "UNKNOWN",
                  permalink: apiData.baseData.permalink,
                  timestamp: new Date(apiData.baseData.timestamp),
                  lastFetched: new Date(),
                  // lastWebhookUpdate hier nicht setzen
                };
                writePromises.push(
                  upsertPost(this.db, postData).catch((e) => {
                    console.error(`DO: D1 upsertPost Error for ${mediaId}`, e);
                    this.logErrorToDoAnalytics(
                      "DO_ALARM_DB_POST_ERROR",
                      "Failed upsertPost",
                      { mediaId, error: String(e) }
                    );
                  })
                );

                // Skip comment processing - comments are not stored
                console.log(
                  `DO ${this.platformConnectionId}: Skipping comment processing for media ${mediaId} (comments not stored)`
                );
                // Enqueue Paginierung
                if (apiData.nextPageUrl) {
                  const queueMessage: PaginationQueueMessage = {
                    platformConnectionId: this.platformConnectionId,
                    mediaId,
                    nextPageUrl: apiData.nextPageUrl,
                  };
                  queuePromises.push(
                    this.env.PAGINATION_QUEUE.send(queueMessage).catch((e) => {
                      console.error(
                        `DO: D3 send PaginationQueue Error for ${mediaId}`,
                        e
                      );
                      this.logErrorToDoAnalytics(
                        "DO_ALARM_QUEUE_ERROR",
                        "Failed sending PaginationQueue",
                        { mediaId, error: String(e) }
                      );
                    })
                  );
                }
              } else {
                // Fehler beim Abruf dieses Items im Batch
                console.error(
                  `DO: API call failed within batch for media ${result.mediaId}`,
                  result.error
                );
                this.logErrorToDoAnalytics(
                  "DO_BATCH_ITEM_ERROR",
                  `API failed for media ${result.mediaId} in batch`,
                  { mediaId: result.mediaId, error: result.error }
                );
                if (result.isAuthError) {
                  // Prüfe auf Auth Fehler
                  await this.markConnectionForReauth(this.platformConnectionId);
                  console.error(
                    `DO ${this.platformConnectionId}: Auth error in batch, stopping alarm processing.`
                  );
                  throw new GraphApiError("Batch Auth Error", 401, false, true); // Signalisiere Abbruch für äußeres Catch
                }
              }
            }
            // Warte auf DB/Queue Operationen dieser Batch (aber nicht ewig)
            const settleTimeout = new Promise((resolve) =>
              setTimeout(resolve, 10000)
            ); // 10s Timeout
            await Promise.race([
              Promise.allSettled([...writePromises, ...queuePromises]),
              settleTimeout,
            ]);
          } else {
            // Ganze Batch fehlgeschlagen (nach Retries im API Client)
            console.error(
              `DO ${this.platformConnectionId}: Entire API batch fetch failed for IDs: ${batchMediaIds.join(", ")}`
            );
            this.logErrorToDoAnalytics(
              "DO_ALARM_BATCH_FAIL",
              `Entire batch API call failed`,
              { mediaIds: batchMediaIds }
            );
            // Hier ggf. Debounce State für diese IDs wiederherstellen? Oder nur loggen.
          }
        } catch (batchError: any) {
          console.error(
            `DO ${this.platformConnectionId}: CRITICAL Error processing batch ${i / GRAPH_API_BATCH_LIMIT + 1}:`,
            batchError
          );
          this.logErrorToDoAnalytics(
            "DO_ALARM_BATCH_ERROR",
            `Critical error processing batch`,
            { error: String(batchError) }
          );
          if (batchError instanceof GraphApiError && batchError.isAuthError) {
            await this.markConnectionForReauth(this.platformConnectionId);
          }
          break; // Breche weitere Batches ab bei kritischem Fehler
        }
      } // Ende Batch Loop
      if (successfulProcessingCount > 0) workDone = true; // Arbeit wurde (teilweise) erfolgreich erledigt
    } else {
      // Nichts zu tun
      workDone = false;
    }

    // 5. Setze nächsten Alarm basierend darauf, ob Arbeit getan wurde
    nextAlarmDelay = workDone ? BUSY_ALARM_INTERVAL_MS : IDLE_ALARM_INTERVAL_MS;
    console.log(
      `DO ${this.platformConnectionId}: ${workDone ? "Work done" : "No work done"}, scheduling next alarm in ${nextAlarmDelay / 1000}s.`
    );
    this.state.waitUntil(
      this.state.storage.setAlarm(Date.now() + nextAlarmDelay)
    );
  } // Ende alarm()

  async markConnectionForReauth(platformConnectionId: string): Promise<void> {
    try {
      console.warn(
        `DO ${this.platformConnectionId}: Marking connection ${platformConnectionId} as auth_needed.`
      );
      await updateConnectionStatus(
        this.db,
        platformConnectionId,
        "auth_needed"
      ); // Nutzt DB Service
    } catch (dbError) {
      console.error(
        `DO ${this.platformConnectionId}: Failed to mark connection ${platformConnectionId} for reauth:`,
        dbError
      );
      this.logErrorToDoAnalytics(
        "DO_MARK_REAUTH_FAIL",
        "Failed DB update to mark reauth",
        { platformConnectionId, error: String(dbError) }
      );
    }
  }

  logErrorToDoAnalytics(type: string, message: string, data: object = {}) {
    const envWithErrors = this.env as Bindings & {
      APP_ERRORS?: AnalyticsEngineDataset;
    };
    if (!envWithErrors.APP_ERRORS) {
      console.error(
        `[NO ANALYTICS IN DO ${this.platformConnectionId}] Error:`,
        type,
        message,
        data
      );
      return;
    }
    try {
      this.state.waitUntil(
        Promise.resolve()
          .then(() => {
            envWithErrors.APP_ERRORS?.writeDataPoint({
              blobs: [
                type,
                message,
                JSON.stringify(data),
                `DO-${this.platformConnectionId}`,
              ],
              indexes: [type, this.platformConnectionId],
            });
          })
          .catch((e) =>
            console.error(
              `DO ${this.platformConnectionId}: Analytics write failed:`,
              e
            )
          )
      );
    } catch (e) {
      console.error(
        `DO ${this.platformConnectionId}: Failed to call Analytics Engine:`,
        e
      );
    }
  }
} // Ende DebouncerDO Klasse
