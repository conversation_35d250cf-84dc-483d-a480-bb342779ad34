"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";

// Type for the projects data returned by the API (includes feeds relation)
// Note: API returns dates as strings, not Date objects
export type ProjectsWithFeeds = Array<{
  id: string;
  name: string;
  description: string | null;
  status: "active" | "paused" | "archived";
  createdAt: string;
  updatedAt: string;
  feeds: Array<{
    id: string;
    name: string;
    description: string | null;
    status: "active" | "paused" | "archived";
    createdAt: string | null;
    updatedAt: string | null;
    lastActivity: string | null;
  }>;
}>;

export const getProjects = async (): Promise<ProjectsWithFeeds> => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects.$get(undefined, {
    init: {
      next: {
        revalidate: 60, // Daten alle 60 Sekunden revalidieren
        tags: ["projects"], // Cache-Tag für gezielte Revalidierung
      },
    },
  });
  const projects = await res.json();
  return projects as ProjectsWithFeeds;
};
