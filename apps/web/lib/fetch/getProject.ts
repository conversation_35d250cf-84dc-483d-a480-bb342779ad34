"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";

// Type for the project data returned by the API (includes feeds relation)
// Note: API returns dates as strings, not Date objects
export type ProjectWithFeeds = {
  id: string;
  name: string;
  description: string | null;
  status: "active" | "paused" | "archived";
  createdAt: string;
  updatedAt: string;
  feeds: Array<{
    id: string;
    name: string;
    description: string | null;
    status: "active" | "paused" | "archived";
    createdAt: string | null;
    updatedAt: string | null;
    lastActivity: string | null;
  }>;
} | null;

export const getProject = async ({
  projectId,
}: {
  projectId: string;
}): Promise<ProjectWithFeeds> => {
  try {
    const accessToken = await getAccessTokenAsync();
    if (!accessToken) throw new Error("No access token");
    const user = await getUser();
    if (!user) throw new Error("No user");
    const orgId = user?.getActiveOrg()?.orgId;
    if (!orgId) throw new Error("No org id");
    const apiClient = createApiClient(accessToken, orgId);
    const res = await apiClient.manage.projects[":projectId"].$get(
      {
        param: {
          projectId,
        },
      },
      {
        init: {
          next: {
            revalidate: 60, // Daten alle 60 Sekunden revalidieren
            tags: ["project", projectId], // Cache-Tag für gezielte Revalidierung
          },
        },
      }
    );
    const project = await res.json();
    return project as ProjectWithFeeds;
  } catch (e) {
    console.error("Error fetching project:", e);
    return null;
  }
};
