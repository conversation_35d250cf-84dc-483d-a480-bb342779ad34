"use client";

import type React from "react";
import { createContext, useContext, useState, useMemo } from "react";

interface ProjectPageContextType {
  pageTitle: string;
  setPageTitle: (title: string) => void;
  projectTitle: string | null;
  setProjectTitle: (title: string | null) => void;
  pageIcon: React.ReactNode;
  setPageIcon: (icon: React.ReactNode) => void;
  headerActions: React.ReactNode;
  setHeaderActions: (actions: React.ReactNode) => void;
  showBackButton: boolean;
  setShowBackButton: (show: boolean) => void;
  backButtonHref: string;
  setBackButtonHref: (href: string) => void;
  backButtonLabel: string;
  setBackButtonLabel: (label: string) => void;
}

const ProjectPageContext = createContext<ProjectPageContextType | undefined>(
  undefined
);

export function ProjectPageProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [pageTitle, setPageTitle] = useState("Overview");
  const [projectTitle, setProjectTitle] = useState<string | null>(null);
  const [pageIcon, setPageIcon] = useState<React.ReactNode>(null);
  const [headerActions, setHeaderActions] = useState<React.ReactNode>(null);
  const [showBackButton, setShowBackButton] = useState(false);
  const [backButtonHref, setBackButtonHref] = useState("");
  const [backButtonLabel, setBackButtonLabel] = useState("Back");

  const contextValue = useMemo(
    () => ({
      pageTitle,
      setPageTitle,
      projectTitle,
      setProjectTitle,
      pageIcon,
      setPageIcon,
      headerActions,
      setHeaderActions,
      showBackButton,
      setShowBackButton,
      backButtonHref,
      setBackButtonHref,
      backButtonLabel,
      setBackButtonLabel,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      pageTitle,
      pageIcon,
      headerActions,
      showBackButton,
      backButtonHref,
      backButtonLabel,
    ]
  );

  return (
    <ProjectPageContext.Provider value={contextValue}>
      {children}
    </ProjectPageContext.Provider>
  );
}

export function useProjectPage() {
  const context = useContext(ProjectPageContext);
  if (context === undefined) {
    throw new Error("useProjectPage must be used within a ProjectPageProvider");
  }
  return context;
}
