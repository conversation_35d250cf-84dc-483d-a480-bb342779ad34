"use server";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { platformConnections } from "@repo/api/src/db/schema";

export const createFeed = async ({
  name,
  projectId,
  description,
  connections,
}: {
  name: string;
  projectId: string;
  description: string;
  connections: string[];
}) => {
  if (!projectId) throw new Error("No project id");
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].feeds.$post({
    param: { projectId },
    json: {
      name,
      description,
      connections,
    },
  });
  const data = await res.json();
  revalidatePath("/dashboard/projects/${projectId}/feeds");
  return data;
};
