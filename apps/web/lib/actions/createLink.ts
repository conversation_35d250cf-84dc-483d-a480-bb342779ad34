"use server";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";

export const createLink = async ({
  name,
  expires,
  projectId,
  connectionId,
}: {
  name: string;
  expires?: Date;
  projectId: string;
  connectionId: string;
}) => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].connections[
    ":connectionId"
  ].links.$post({
    json: {
      name,
      expires: expires?.toISOString(),
    },
    param: {
      projectId,
      connectionId,
    },
  });
  const data = await res.json();
  revalidatePath(
    "/dashboard/projects/${projectId}/connections/${connectionId}"
  );
  return data;
};
