import { Feed } from "@repo/api/src/types";
import { ArrowBigDown } from "lucide-react";
import React from "react";
import NetworkStatusIcon from "./NetworkStatusIcon";

const NetworkIndicators: React.FC<{
  networks?: { platform: string | null; status: string | null }[];
}> = ({ networks }) => {
  return (
    <div className="flex flex-wrap gap-2">
      {networks?.map((network) => {
        const IconComponent = ArrowBigDown;
        const isActivated = network.status === "active";

        if (!isActivated) {
          return (
            <div
              key={network.platform}
              className="flex items-center space-x-1 px-2 py-1 rounded-md bg-gray-100 text-gray-400"
              title={`${network.platform} - Disabled`}
            >
              <IconComponent className="h-3 w-3" />
              <span className="text-xs">Disabled</span>
            </div>
          );
        }

        return (
          <div
            key={network.platform}
            className={`flex items-center space-x-1 px-2 py-1 rounded-md ${
              network.status === "connected"
                ? `bg-green-500 text-black`
                : network.status === "error"
                  ? "bg-red-100 text-red-600"
                  : "bg-gray-100 text-gray-400"
            }`}
            title={`${network.platform} - ${network.status === "connected" ? "Connected" : network.status === "error" ? "Error" : "Disconnected"}`}
          >
            <IconComponent className="h-3 w-3" />
            <NetworkStatusIcon status={network.status ?? "error"} />
            <span className="text-xs font-medium">{network.platform}</span>
          </div>
        );
      })}
    </div>
  );
};

export default NetworkIndicators;
