import { AlertCircle, CheckCircle, XCircle } from "lucide-react";
import React from "react";

const StatusIcon: React.FC<{ status: string }> = ({ status }) => {
  switch (status) {
    case "active":
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case "paused":
      return <AlertCircle className="h-4 w-4 text-orange-500" />;
    case "error":
      return <XCircle className="h-4 w-4 text-red-500" />;
    default:
      return <XCircle className="h-4 w-4 text-gray-400" />;
  }
};

export default StatusIcon;
