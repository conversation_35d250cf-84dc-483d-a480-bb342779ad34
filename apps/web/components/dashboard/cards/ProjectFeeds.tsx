import React from "react";
import { Building } from "lucide-react";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import AnalyticsCard from "./AnalyticsCard";

const getFeedCount = async (projectId: string) => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const org = user.getActiveOrg();
  const orgId = org?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  // --- DER MAGISCHE TEIL: Typsicherer API-Aufruf ---
  const res = await apiClient.manage.count.feeds[":projectId"].$get(
    {
      param: {
        projectId,
      },
    },
    {
      init: {
        next: {
          revalidate: 60, // Daten alle 60 Sekunden revalidieren
          tags: ["count", "feeds"], // Cache-Tag für gezielte Revalidierung
        },
      },
    }
  );
  const data = await res.json();
  return data;
};

const ProjectFeedsCard: React.FC<{ projectId: string }> = async ({
  projectId,
}) => {
  const count = await getFeedCount(projectId);
  return (
    <AnalyticsCard
      className="text-card-feeds-foreground bg-card-feeds"
      icon={<Building className="h-9 w-9 absolute right-5 top-4" />}
      title="Total Feeds"
      value={count.feeds}
      description={`${count.activeFeeds} active`}
    />
  );
};

export default ProjectFeedsCard;
