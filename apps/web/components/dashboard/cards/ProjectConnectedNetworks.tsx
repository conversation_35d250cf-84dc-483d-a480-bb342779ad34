import React from "react";
import { TrendingUp } from "lucide-react";
import AnalyticsCard from "./AnalyticsCard";

const ProjectConnectedNetworks = () => {
  return (
    <AnalyticsCard
      className="text-card-networks-foreground bg-card-networks"
      icon={<TrendingUp className="h-9 w-9 absolute right-5 top-4" />}
      title="Connected Networks"
      value={0}
      description={`Total connections`}
    />
  );
};

export default ProjectConnectedNetworks;
