"use client";
import React, { useEffect, useState } from "react";
import {
  AlertCircle,
  Facebook,
  Instagram,
  Plus,
  Twitter,
  Youtube,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { getConnections } from "@/lib/fetch/getConnections";
import { availableNetworks } from "@/constants/availableNetworks";
import { set } from "react-hook-form";
import { createFeed } from "@/lib/actions/createFeed";

// Mock data for connections (should be fetched for the project)
const MOCK_PROJECT_CONNECTIONS = [
  {
    id: "conn_001",
    name: "Company Facebook Page",
    platform: "Facebook",
    icon: Facebook,
  },
  {
    id: "conn_002",
    name: "Company Instagram",
    platform: "Instagram",
    icon: Instagram,
  },
  {
    id: "conn_003",
    name: "Company YouTube",
    platform: "YouTube",
    icon: Youtube,
  },
  {
    id: "conn_004",
    name: "Personal Twitter",
    platform: "Twitter",
    icon: Twitter,
  },
];

const CreateFeedButton: React.FC<{
  projectId: string;
  connections: Awaited<ReturnType<typeof getConnections>>;
}> = ({ projectId, connections }) => {
  const router = useRouter();

  const [isCreateFeedOpen, setIsCreateFeedOpen] = useState(false);
  const [newFeedName, setNewFeedName] = useState("");
  const [newFeedDescription, setNewFeedDescription] = useState("");
  const [selectedConnections, setSelectedConnections] = useState<string[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);

  const handleConnectionToggle = (connectionId: string, platform?: string) => {
    setSelectedConnections((prev) =>
      prev.includes(connectionId)
        ? prev.filter((id) => id !== connectionId)
        : [...prev, connectionId]
    );
  };

  useEffect(() => {
    setSelectedPlatform(
      connections.find((conn) => conn.id === selectedConnections[0])
        ?.platform ?? null
    );
  }, [selectedConnections]);

  const handleCreateFeed = async () => {
    if (!newFeedName || selectedConnections.length === 0) {
      toast({
        title: "Missing Information",
        description:
          "Please provide a feed name and select at least one connection.",
        variant: "destructive",
      });
      return;
    }
    // Mock creation logic
    await createFeed({
      name: newFeedName,
      projectId: projectId,
      description: newFeedDescription,
      connections: selectedConnections,
    });
    toast({
      title: "Feed created",
      description: `New feed "${newFeedName}" has been created with ${selectedConnections.length} connection(s).`,
    });
    setIsCreateFeedOpen(false);
    setNewFeedName("");
    setNewFeedDescription("");
    setSelectedConnections([]);
    setSelectedPlatform(null);
  };

  return (
    <Dialog open={isCreateFeedOpen} onOpenChange={setIsCreateFeedOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Feed
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Create New Feed</DialogTitle>
          <DialogDescription>
            Create a new feed to aggregate data from your connections.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="feed-name">Feed Name</Label>
            <Input
              id="feed-name"
              placeholder="e.g., Main Social Feed"
              value={newFeedName}
              onChange={(e) => setNewFeedName(e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="feed-description">Description (Optional)</Label>
            <Input
              id="feed-description"
              placeholder="e.g., Aggregated feed from all social platforms"
              value={newFeedDescription}
              onChange={(e) => setNewFeedDescription(e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label>Select Connections</Label>
            <Card className="max-h-60 overflow-y-auto">
              <CardContent className="p-4 space-y-3">
                {connections.length > 0 ? (
                  connections.map((conn) => {
                    const IconComponent =
                      availableNetworks.find((n) => n.slug === conn.platform)
                        ?.icon || AlertCircle;
                    return (
                      <div
                        key={conn.id}
                        className="flex items-center space-x-3 p-2 rounded-md hover:bg-muted/50"
                      >
                        <Checkbox
                          id={`conn-${conn.id}`}
                          checked={selectedConnections.includes(conn.id)}
                          onCheckedChange={() => {
                            handleConnectionToggle(conn.id, conn.platform);
                          }}
                          disabled={
                            !!selectedPlatform &&
                            selectedPlatform !== conn.platform
                          }
                        />
                        <IconComponent className="h-5 w-5 text-muted-foreground" />
                        <Label
                          htmlFor={`conn-${conn.id}`}
                          className="flex-1 cursor-pointer"
                        >
                          {conn.name}{" "}
                          <span className="text-xs text-muted-foreground">
                            ({conn.platform})
                          </span>
                          {!!selectedPlatform &&
                            selectedPlatform !== conn.platform && (
                              <p className="text-xs text-red-500">
                                Please only select connections of the same
                                platform.
                              </p>
                            )}
                        </Label>
                      </div>
                    );
                  })
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No connections available. Please create a connection first.
                  </p>
                )}
              </CardContent>
            </Card>
            {selectedConnections.length === 0 && (
              <p className="text-xs text-red-500">
                Please select at least one connection.
              </p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsCreateFeedOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateFeed}
            disabled={!newFeedName || selectedConnections.length === 0}
          >
            Create Feed
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateFeedButton;
