"use client";
import { useProjectPage } from "@/lib/contexts/project-page-context";
import React, { useEffect } from "react";

const ProjectHeaderInformationSetter = ({
  pageTitle,
  projectTitle,
  pageIcon,
  showBackButton,
  backButtonHref,
  backButtonLabel,
}: {
  pageTitle: string;
  projectTitle: string | null;
  pageIcon: React.ReactNode;
  showBackButton: boolean;
  backButtonHref?: string | null;
  backButtonLabel?: string | null;
}) => {
  const {
    setPageTitle,
    setProjectTitle,
    setPageIcon,
    setShowBackButton,
    setBackButtonHref,
    setBackButtonLabel,
  } = useProjectPage();

  useEffect(() => {
    setPageTitle(pageTitle);
  }, [setPageTitle, pageTitle]);

  useEffect(() => {
    setProjectTitle(projectTitle);
  }, [setProjectTitle, projectTitle]);

  useEffect(() => {
    setPageIcon(pageIcon);
  }, [setPageIcon, pageIcon]);

  useEffect(() => {
    setShowBackButton(showBackButton);
  }, [setShowBackButton, showBackButton]);

  useEffect(() => {
    if (backButtonHref) setBackButtonHref(backButtonHref);
  }, [setBackButtonHref, backButtonHref]);

  useEffect(() => {
    if (backButtonLabel) setBackButtonLabel(backButtonLabel);
  }, [setBackButtonLabel, backButtonLabel]);

  return null;
};

export default ProjectHeaderInformationSetter;
