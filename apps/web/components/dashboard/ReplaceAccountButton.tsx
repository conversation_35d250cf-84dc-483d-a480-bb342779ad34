"use client";
import React from "react";
import { Button } from "../ui/button";
import { toast } from "../ui/use-toast";

const ReplaceAccountButton = ({ connectionId }: { connectionId: string }) => {
  const handleReplaceAccount = () => {
    // 1. Simulate generating a unique link token for replacement
    const newLinkToken = `replacetoken_${connectionId}_${Date.now().toString(36)}`;
    const projectCreatorEmail = "<EMAIL>";
    // 2. In a real app, you would make an API call here to:
    //    - Create a new shareable link record in your database.
    //    - Associate it with the current project (currentProjectId),
    //      the platform (connection.slug),
    //      and mark it for "replacement" of this specific connection.
    //    - The API would return this newLinkToken.

    toast({
      title: "Initiating Account Replacement",
      description: "You'll be redirected to connect the new account.",
    });

    /*
    // 3. Redirect to the shareable link landing page
    const queryParams = new URLSearchParams({
      projectId: connection.projectId, // Use the projectId from the connection data
      platformSlug: connection.slug,
      purpose: "replaceAccount",
      creatorEmail: projectCreatorEmail, // Pass creator's email
      originalConnectionName: connection.name, // Pass current connection name for context
    });
    */
    //router.push(`/connect/link/${newLinkToken}?${queryParams.toString()}`);
  };
  return (
    <Button size="sm" variant="outline" onClick={handleReplaceAccount}>
      Replace Account
    </Button>
  );
};

export default ReplaceAccountButton;
