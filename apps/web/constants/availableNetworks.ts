import { Facebook, Instagram, Twitter, Youtube } from "lucide-react";

// Available networks configuration
export const availableNetworks = [
  {
    name: "Facebook",
    slug: "facebook",
    icon: Facebook,
    description: "Pages, posts, and insights",
    color: "text-blue-600",
    bgColor: "bg-blue-100",
  },
  {
    name: "Instagram",
    slug: "instagram",
    icon: Instagram,
    description: "Posts, stories, and engagement",
    color: "text-pink-600",
    bgColor: "bg-pink-100",
  },
  {
    name: "Twitter",
    slug: "twitter",
    icon: Twitter,
    description: "Tweets, mentions, and trends",
    color: "text-sky-600",
    bgColor: "bg-sky-100",
  },
  {
    name: "YouTube",
    slug: "youtube",
    icon: Youtube,
    description: "Videos, comments, and analytics",
    color: "text-red-600",
    bgColor: "bg-red-100",
  },
];
