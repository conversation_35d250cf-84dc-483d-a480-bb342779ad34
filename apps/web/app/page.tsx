import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Code,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Star,
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  Database,
  LinkIcon,
  Filter,
  BarChart3,
  Key,
} from "lucide-react";
import Link from "next/link";

const features = [
  {
    icon: LinkIcon,
    title: "Secure Login Links",
    description:
      "Generate secure login links for clients to connect their social media accounts without sharing passwords.",
  },
  {
    icon: Code,
    title: "Unified API",
    description:
      "One simple API to access data from all major social platforms. No need to learn multiple APIs or handle different authentication flows.",
  },
  {
    icon: Zap,
    title: "Rate Limit Management",
    description:
      "We handle all rate limiting, retries, and API quotas across platforms so you don't have to worry about implementation details.",
  },
  {
    icon: Filter,
    title: "Data Aggregation & Filtering",
    description:
      "Get aggregated data across platforms with powerful filtering, sorting, and transformation capabilities built-in.",
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description:
      "Bank-level security with OAuth 2.0, encrypted data transmission, and comprehensive audit logging.",
  },
  {
    icon: Database,
    title: "Flexible Data Export",
    description:
      "Export data in JSON, CSV, or connect directly via webhooks. Use the data however your application needs it.",
  },
];

const socialPlatforms = [
  { name: "Facebook", icon: Facebook, color: "text-blue-600" },
  { name: "Instagram", icon: Instagram, color: "text-pink-600" },
  { name: "Twitter", icon: Twitter, color: "text-sky-600" },
  { name: "YouTube", icon: Youtube, color: "text-red-600" },
];

const testimonials = [
  {
    name: "Alex Chen",
    role: "Lead Developer",
    company: "MarketingTech Inc",
    content:
      "This API saved us months of development time. Instead of implementing 6 different social media integrations, we just use one clean API.",
    rating: 5,
  },
  {
    name: "Sarah Rodriguez",
    role: "CTO",
    company: "DataFlow Solutions",
    content:
      "The secure login links are a game-changer. Our clients can connect their accounts without sharing credentials, and we get the data we need.",
    rating: 5,
  },
  {
    name: "Michael Thompson",
    role: "Full Stack Developer",
    company: "SocialSync",
    content:
      "Rate limiting was always a nightmare with social APIs. This platform handles all of that complexity for us automatically.",
    rating: 5,
  },
];

const pricingPlans = [
  {
    name: "Developer",
    price: "$49",
    period: "per month",
    description: "Perfect for individual developers and small projects",
    features: [
      "100K API calls/month",
      "5 connected accounts",
      "Basic data filtering",
      "Email support",
      "Standard rate limits",
    ],
    popular: false,
  },
  {
    name: "Professional",
    price: "$149",
    period: "per month",
    description: "Ideal for growing applications and teams",
    features: [
      "1M API calls/month",
      "50 connected accounts",
      "Advanced aggregation & filtering",
      "Priority support",
      "Webhook notifications",
      "Custom data transformations",
    ],
    popular: true,
  },
  {
    name: "Enterprise",
    price: "$499",
    period: "per month",
    description: "For large-scale applications with high volume needs",
    features: [
      "10M+ API calls/month",
      "Unlimited connected accounts",
      "Real-time data streaming",
      "24/7 dedicated support",
      "Custom integrations",
      "SLA guarantees",
      "On-premise deployment options",
    ],
    popular: false,
  },
];

const codeExample = `// Simple API call to get aggregated social media data
const response = await fetch('https://api.socialanalytics.com/v1/data', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  method: 'POST',
  body: JSON.stringify({
    accounts: ['facebook_page_123', 'instagram_account_456'],
    metrics: ['followers', 'engagement', 'posts'],
    date_range: '30d',
    aggregation: 'sum'
  })
});

const data = await response.json();
// Get unified data from multiple platforms instantly`;

export default function MarketingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Zap className="size-4" />
              </div>
              <span className="text-xl font-bold">Social Feed API</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link
                href="#features"
                className="text-gray-600 hover:text-gray-900"
              >
                Features
              </Link>
              <Link
                href="#pricing"
                className="text-gray-600 hover:text-gray-900"
              >
                Pricing
              </Link>
              <Link href="#docs" className="text-gray-600 hover:text-gray-900">
                Docs
              </Link>
              <Link href="/dashboard">
                <Button variant="outline">Sign In</Button>
              </Link>
              <Link href="/dashboard">
                <Button>Get API Key</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <Badge variant="secondary" className="mb-4">
              🚀 Now supporting 10+ social platforms
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Social Media Integration
              <span className="text-primary"> Made Simple</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Skip the complexity of multiple social media APIs. Get unified
              access to social data with secure login links, automatic rate
              limiting, and powerful data aggregation - all through one clean
              API.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard">
                <Button size="lg" className="text-lg px-8">
                  Start Building
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button size="lg" variant="outline" className="text-lg px-8">
                View Documentation
              </Button>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              Free tier available • No credit card required
            </p>
          </div>
        </div>
      </section>

      {/* Code Example */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold mb-4">
                One API, All Platforms
              </h2>
              <p className="text-gray-300">
                Get started with just a few lines of code
              </p>
            </div>
            <div className="bg-gray-800 rounded-lg p-6 overflow-x-auto">
              <pre className="text-sm text-gray-300">
                <code>{codeExample}</code>
              </pre>
            </div>
          </div>
        </div>
      </section>

      {/* Social Platforms */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-gray-600 mb-8">
              Integrate with all major social media platforms
            </p>
            <div className="flex justify-center items-center space-x-8 md:space-x-12">
              {socialPlatforms.map((platform) => {
                const IconComponent = platform.icon;
                return (
                  <div
                    key={platform.name}
                    className="flex flex-col items-center space-y-2"
                  >
                    <IconComponent className={`h-8 w-8 ${platform.color}`} />
                    <span className="text-sm text-gray-600">
                      {platform.name}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Built for Developers
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Focus on building your application, not wrestling with social
              media APIs and authentication flows.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card
                  key={index}
                  className="border-0 shadow-lg hover:shadow-xl transition-shadow"
                >
                  <CardHeader>
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">10M+</div>
              <div className="text-primary-foreground/80">
                API Calls Processed
              </div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">5K+</div>
              <div className="text-primary-foreground/80">Developers</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">99.9%</div>
              <div className="text-primary-foreground/80">API Uptime</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50ms</div>
              <div className="text-primary-foreground/80">
                Avg Response Time
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How it Works */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600">
              Three simple steps to get social media data
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Key className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">
                1. Generate Login Links
              </h3>
              <p className="text-gray-600">
                Create secure login links for your clients to connect their
                social media accounts without sharing passwords.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Database className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">
                2. Access Unified Data
              </h3>
              <p className="text-gray-600">
                Use our single API to get data from all connected platforms with
                automatic rate limiting and error handling.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">3. Build Your App</h3>
              <p className="text-gray-600">
                Focus on your application logic while we handle the complexity
                of social media integrations.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Trusted by Developers
            </h2>
            <p className="text-xl text-gray-600">
              See what developers are saying about our API
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4">"{testimonial.content}"</p>
                  <div>
                    <div className="font-semibold">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">
                      {testimonial.role} at {testimonial.company}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Simple, Usage-Based Pricing
            </h2>
            <p className="text-xl text-gray-600">
              Pay only for what you use, scale as you grow
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`relative border-0 shadow-lg ${plan.popular ? "ring-2 ring-primary" : ""}`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">{plan.price}</span>
                    <span className="text-gray-600">/{plan.period}</span>
                  </div>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link href="/dashboard">
                    <Button
                      className="w-full"
                      variant={plan.popular ? "default" : "outline"}
                    >
                      Get Started
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to simplify your social integrations?
          </h2>
          <p className="text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            Join thousands of developers who trust our API to handle their
            social media data needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" variant="secondary" className="text-lg px-8">
                Start Building Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button
              size="lg"
              variant="outline"
              className="text-lg px-8 border-white text-white hover:bg-white hover:text-primary"
            >
              Read Documentation
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                  <Zap className="size-4" />
                </div>
                <span className="text-xl font-bold">Social Feed API</span>
              </div>
              <p className="text-gray-400">
                The simplest way to integrate social media data into your
                applications.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="#features" className="hover:text-white">
                    Features
                  </Link>
                </li>
                <li>
                  <Link href="#pricing" className="hover:text-white">
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="hover:text-white">
                    Dashboard
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Developers</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="#" className="hover:text-white">
                    API Documentation
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:text-white">
                    SDKs & Libraries
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:text-white">
                    Code Examples
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/help" className="hover:text-white">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:text-white">
                    Contact Support
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:text-white">
                    Status Page
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Social API. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
