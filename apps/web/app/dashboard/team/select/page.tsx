"use client";
// A Page to select an organization
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useUser } from "@propelauth/nextjs/client";

export default function SelectTeamPage() {
  const router = useRouter();
  const { user, setActiveOrg } = useUser();

  const handleSelectOrg = async (orgId: string) => {
    const newUser = await setActiveOrg(orgId);

    if (newUser?.activeOrgId === orgId) {
      toast({
        title: "Organization selected",
      });
      router.push("/dashboard");
    } else {
      toast({
        title: "Error",
        description: "Failed to select organization",
        variant: "destructive",
      });
    }
  };

  // A List of all Organizations to select one. Also A Button to create a org that will redirect to the hosted propelauth ui
  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex-1 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Select Your Team</CardTitle>
            <CardDescription>
              Select the team you want to work with or create a new one.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {user?.getOrgs().map((org) => (
                <div
                  key={org.orgId}
                  className="flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors hover:bg-muted/50"
                  onClick={() => handleSelectOrg(org.orgId)}
                >
                  <div className="flex items-center space-x-3">
                    <span>{org.orgName}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={() => {
                window.location.href = `${process.env.NEXT_PUBLIC_AUTH_URL}/create_org`;
              }}
            >
              Create Organization
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
