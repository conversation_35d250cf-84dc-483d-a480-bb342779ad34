"use client"

import { useState, useEffect } from "react"
import {
  ArrowLeft,
  Eye,
  EyeOff,
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  CheckCircle,
  AlertCircle,
  Info,
  Lock,
  Building,
} from "lucide-react"
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { toast } from "@/hooks/use-toast"
import Link from "next/link"

const platformConfig = {
  facebook: {
    name: "Facebook",
    icon: Facebook,
    color: "bg-blue-600",
    description: "Connect your Facebook account to start collecting data from your pages and posts.",
    hasAccountTypes: false,
  },
  instagram: {
    name: "Instagram",
    icon: Instagram,
    color: "bg-gradient-to-r from-purple-500 to-pink-500",
    description: "Connect your Instagram account to analyze posts, stories, and engagement metrics.",
    hasAccountTypes: true,
    accountTypes: [
      {
        id: "business",
        name: "Business/Creator Account",
        icon: Building,
        description: "Full access to insights, analytics, and all public data",
        permissions: [
          "Read post insights and analytics",
          "Access story metrics and data",
          "View detailed audience demographics",
          "Track engagement rates and reach",
          "Access hashtag performance data",
        ],
        limitations: [],
      },
      {
        id: "personal",
        name: "Personal/Private Account",
        icon: Lock,
        description: "Limited access due to privacy restrictions",
        permissions: [
          "Read basic profile information",
          "Access your own posts and media",
          "View basic engagement metrics",
        ],
        limitations: [
          "No access to detailed analytics",
          "Limited audience insights",
          "Cannot access follower demographics",
          "Reduced engagement metrics",
          "No hashtag performance data",
        ],
      },
    ],
  },
  twitter: {
    name: "Twitter",
    icon: Twitter,
    color: "bg-sky-500",
    description: "Connect your Twitter account to monitor tweets, mentions, and trending topics.",
    hasAccountTypes: false,
  },
  youtube: {
    name: "YouTube",
    icon: Youtube,
    color: "bg-red-600",
    description: "Connect your YouTube channel to track video performance and audience insights.",
    hasAccountTypes: false,
  },
}

type Platform = keyof typeof platformConfig

export default function ConnectPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const platform = params.platform as Platform
  const token = searchParams.get("token")
  const [step, setStep] = useState<"account-type" | "login" | "2fa" | "success">("account-type")
  const [selectedAccountType, setSelectedAccountType] = useState<string>("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [twoFactorCode, setTwoFactorCode] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  const config = platformConfig[platform]

  useEffect(() => {
    if (!config) {
      router.push("/")
    }
    // Skip account type selection for platforms that don't have it
    if (config && !config.hasAccountTypes) {
      setStep("login")
    }
  }, [config, router])

  if (!config) {
    return null
  }

  const IconComponent = config.icon
  const selectedAccount = config.hasAccountTypes
    ? config.accountTypes?.find((type) => type.id === selectedAccountType)
    : null

  const validateAccountType = () => {
    if (config.hasAccountTypes && !selectedAccountType) {
      setErrors({ accountType: "Please select an account type" })
      return false
    }
    setErrors({})
    return true
  }

  const validateLogin = () => {
    const newErrors: { [key: string]: string } = {}

    if (!email) {
      newErrors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!password) {
      newErrors.password = "Password is required"
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validate2FA = () => {
    const newErrors: { [key: string]: string } = {}

    if (!twoFactorCode) {
      newErrors.twoFactorCode = "2FA code is required"
    } else if (twoFactorCode.length !== 6) {
      newErrors.twoFactorCode = "2FA code must be 6 digits"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleAccountTypeNext = () => {
    if (!validateAccountType()) return
    setStep("login")
  }

  const handleLogin = async () => {
    if (!validateLogin()) return

    setIsLoading(true)
    // Mock login delay
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Mock login success - in real app, this would call the actual API
    setIsLoading(false)
    setStep("2fa")
    toast({
      title: "Login successful",
      description: "Please enter your 2FA code to complete the connection.",
    })
  }

  const handleTwoFactor = async () => {
    if (!validate2FA()) return

    setIsLoading(true)
    // Mock 2FA verification delay
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Mock 2FA success
    setIsLoading(false)
    setStep("success")
    toast({
      title: "Connection successful",
      description: `Your ${config.name} account has been connected successfully.`,
    })
  }

  const handleFinish = () => {
    if (token) {
      // Redirect back to the connection link page
      router.push(`/link/${token}`)
    } else {
      // Redirect to dashboard if no token (direct access)
      router.push("/")
    }
  }

  const getBackUrl = () => {
    if (token) {
      return `/link/${token}`
    }
    return "/"
  }

  const getStepNumber = () => {
    const steps = config.hasAccountTypes ? ["account-type", "login", "2fa", "success"] : ["login", "2fa", "success"]
    return steps.indexOf(step) + 1
  }

  const getTotalSteps = () => {
    return config.hasAccountTypes ? 4 : 3
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="flex h-16 items-center px-6">
          <div className="flex items-center space-x-4">
            <Link href={getBackUrl()}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {token ? "Back to Connection" : "Back to Dashboard"}
              </Button>
            </Link>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-3">
              <IconComponent className="h-5 w-5" />
              <h1 className="text-xl font-semibold">Connect {config.name}</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 max-w-2xl mx-auto">
        <div className="space-y-6">
          {/* Platform Header */}
          <div className="text-center space-y-4">
            <div className={`w-16 h-16 rounded-full ${config.color} flex items-center justify-center mx-auto`}>
              <IconComponent className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Connect to {config.name}</h2>
              <p className="text-muted-foreground mt-2">{config.description}</p>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-center space-x-2">
            {Array.from({ length: getTotalSteps() }, (_, i) => (
              <div key={i} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    i + 1 <= getStepNumber() ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                  }`}
                >
                  {i + 1 === getTotalSteps() && step === "success" ? <CheckCircle className="h-4 w-4" /> : i + 1}
                </div>
                {i < getTotalSteps() - 1 && <div className="w-8 h-px bg-border mx-2"></div>}
              </div>
            ))}
          </div>

          {/* Account Type Selection Step (Instagram only) */}
          {step === "account-type" && config.hasAccountTypes && (
            <Card>
              <CardHeader>
                <CardTitle>Select Account Type</CardTitle>
                <CardDescription>
                  Choose your Instagram account type to determine available features and data access.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <RadioGroup value={selectedAccountType} onValueChange={setSelectedAccountType}>
                  {config.accountTypes?.map((accountType) => {
                    const AccountIcon = accountType.icon
                    return (
                      <div key={accountType.id} className="space-y-3">
                        <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-muted/50">
                          <RadioGroupItem value={accountType.id} id={accountType.id} />
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <AccountIcon className="h-5 w-5" />
                              <div>
                                <Label htmlFor={accountType.id} className="font-medium cursor-pointer">
                                  {accountType.name}
                                </Label>
                                <p className="text-sm text-muted-foreground">{accountType.description}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {selectedAccountType === accountType.id && (
                          <div className="ml-8 space-y-4">
                            <div>
                              <h4 className="font-medium text-green-700 mb-2">✓ Available Features:</h4>
                              <ul className="text-sm space-y-1">
                                {accountType.permissions.map((permission, index) => (
                                  <li key={index} className="flex items-center space-x-2">
                                    <div className="w-1.5 h-1.5 bg-green-600 rounded-full" />
                                    <span>{permission}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>

                            {accountType.limitations.length > 0 && (
                              <div>
                                <h4 className="font-medium text-orange-700 mb-2">⚠ Limitations:</h4>
                                <ul className="text-sm space-y-1">
                                  {accountType.limitations.map((limitation, index) => (
                                    <li key={index} className="flex items-center space-x-2">
                                      <div className="w-1.5 h-1.5 bg-orange-600 rounded-full" />
                                      <span>{limitation}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}

                            {accountType.id === "personal" && (
                              <Alert>
                                <Info className="h-4 w-4" />
                                <AlertDescription>
                                  <strong>Note:</strong> Private accounts have limited data access due to Instagram's
                                  privacy policies. Consider switching to a Business account for full analytics and
                                  insights.
                                </AlertDescription>
                              </Alert>
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </RadioGroup>

                {errors.accountType && <p className="text-sm text-red-500">{errors.accountType}</p>}

                <Button onClick={handleAccountTypeNext} disabled={!selectedAccountType} className="w-full">
                  Continue with {selectedAccount?.name || "Selected Account"}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Login Step */}
          {step === "login" && (
            <Card>
              <CardHeader>
                <CardTitle>Sign in to {config.name}</CardTitle>
                <CardDescription>
                  {selectedAccount
                    ? `Enter your ${config.name} credentials for your ${selectedAccount.name.toLowerCase()}.`
                    : `Enter your ${config.name} credentials to continue.`}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedAccount?.id === "personal" && (
                  <Alert>
                    <Lock className="h-4 w-4" />
                    <AlertDescription>
                      You're connecting a private account. Some features will be limited due to privacy restrictions.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className={errors.email ? "border-red-500" : ""}
                  />
                  {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className={errors.password ? "border-red-500" : ""}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                </div>
                <Button onClick={handleLogin} disabled={isLoading} className="w-full">
                  {isLoading ? "Signing in..." : "Sign In"}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* 2FA Step */}
          {step === "2fa" && (
            <Card>
              <CardHeader>
                <CardTitle>Two-Factor Authentication</CardTitle>
                <CardDescription>
                  Enter the 6-digit code from your authenticator app or SMS to complete the connection.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="2fa-code">Authentication Code</Label>
                  <Input
                    id="2fa-code"
                    type="text"
                    placeholder="000000"
                    maxLength={6}
                    value={twoFactorCode}
                    onChange={(e) => setTwoFactorCode(e.target.value.replace(/\D/g, ""))}
                    className={`text-center text-lg tracking-widest ${errors.twoFactorCode ? "border-red-500" : ""}`}
                  />
                  {errors.twoFactorCode && <p className="text-sm text-red-500">{errors.twoFactorCode}</p>}
                </div>
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">
                    Didn't receive a code?{" "}
                    <Button variant="link" className="p-0 h-auto">
                      Resend code
                    </Button>
                  </p>
                </div>
                <Button onClick={handleTwoFactor} disabled={isLoading} className="w-full">
                  {isLoading ? "Verifying..." : "Verify Code"}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Success Step */}
          {step === "success" && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Connection Successful!</h3>
                    <p className="text-muted-foreground">
                      Your {config.name} {selectedAccount ? selectedAccount.name.toLowerCase() : "account"} has been
                      connected successfully.
                    </p>
                    {selectedAccount?.id === "personal" && (
                      <p className="text-sm text-orange-600 mt-2">
                        Remember: Some features are limited for private accounts.
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Button onClick={handleFinish} className="w-full">
                      {token ? "Continue with Connection" : "Return to Dashboard"}
                    </Button>
                    {token && (
                      <Button variant="outline" className="w-full">
                        Connect Another Account
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security Notice */}
          <div className="flex items-start space-x-2 p-4 bg-muted rounded-lg">
            <AlertCircle className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="text-sm text-muted-foreground">
              <p className="font-medium">Security Notice</p>
              <p>Your credentials are encrypted and securely stored. We never store your password or 2FA codes.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
