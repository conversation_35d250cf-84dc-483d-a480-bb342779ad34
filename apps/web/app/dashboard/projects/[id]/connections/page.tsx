import Link from "next/link";
import {
  Settings,
  Facebook,
  Instagram,
  Youtube,
  CheckCircle,
  XCircle,
  AlertCircle,
  Bell,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getConnections } from "@/lib/fetch/getConnections";
import CreateConnectionButton from "@/components/dashboard/CreateConnectionButton";

// Mock data for connections
const connectionsData = [
  {
    id: "conn_001",
    name: "Company Facebook Page",
    platform: "Facebook",
    slug: "facebook",
    icon: Facebook,
    status: "active",
    lastSync: "2 hours ago",
    connectedAccount: { name: "@company_page" },
  },
  {
    id: "conn_002",
    name: "Company Instagram",
    platform: "Instagram",
    slug: "instagram",
    icon: Instagram,
    status: "active",
    lastSync: "1 hour ago",
    connectedAccount: { name: "@company_insta" },
  },
  {
    id: "conn_003",
    name: "Company YouTube",
    platform: "YouTube",
    slug: "youtube",
    icon: Youtube,
    status: "error",
    lastSync: "3 days ago",
    connectedAccount: { name: "Company Channel" },
  },
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case "active":
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case "error":
      return <XCircle className="h-4 w-4 text-red-500" />;
    default:
      return <AlertCircle className="h-4 w-4 text-gray-400" />;
  }
};

const getPlatformIcon = (platform: string) => {
  switch (platform.toLowerCase()) {
    case "facebook":
      return Facebook;
    case "instagram":
      return Instagram;
    case "youtube":
      return Youtube;
    default:
      return AlertCircle;
  }
};

const getStatusBadge = (status: string) => {
  const variants = { active: "default", error: "destructive" } as const;
  return (
    <Badge variant={variants[status as keyof typeof variants] || "outline"}>
      {status}
    </Badge>
  );
};

export default async function ConnectionsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: projectId } = await params;

  const connections = await getConnections(projectId);

  console.log("CONNECTIONS", connections);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Social Media Connections</CardTitle>
              <CardDescription>
                Manage your social media platform connections. Each connection
                can generate OAuth2.0 links.
              </CardDescription>
            </div>
            <CreateConnectionButton projectId={projectId} />
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Connection</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Sync</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {connections.map((connection) => {
                const IconComponent = getPlatformIcon(connection.platform);
                return (
                  <TableRow key={connection.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <IconComponent className="h-5 w-5" />
                        <div>
                          <div className="font-medium">{connection.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {connection.platformAccountName || "Not connected"}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{connection.platform}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(connection.status)}
                        {getStatusBadge(connection.status)}
                      </div>
                    </TableCell>
                    <TableCell>{connection.lastPolledAt ?? "Never"}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Link
                          href={`/dashboard/projects/${projectId}/connections/${connection.id}`}
                        >
                          <Button size="sm" variant="outline">
                            <Settings className="h-4 w-4 mr-1" />
                            Manage
                          </Button>
                        </Link>
                        <Link
                          href={`/dashboard/projects/${projectId}/connections/${connection.id}/notifications`}
                        >
                          <Button
                            size="sm"
                            variant="outline"
                            title="Notification Settings"
                          >
                            <Bell className="h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
