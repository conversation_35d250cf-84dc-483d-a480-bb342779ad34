import { getConnection } from "@/lib/fetch/getConnection";
import React from "react";

const ConnectConnectionPage = async ({
  params,
}: {
  params: Promise<{ id: string; connectionId: string }>;
}) => {
  const { id, connectionId } = await params;

  const connection = await getConnection(id, connectionId);

  return <pre>{JSON.stringify(connection, null, "\t")}</pre>;
};

export default ConnectConnectionPage;
