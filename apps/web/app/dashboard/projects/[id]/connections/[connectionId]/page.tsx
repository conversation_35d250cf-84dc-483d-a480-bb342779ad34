import {
  Facebook,
  CheckCircle,
  XCircle,
  AlertCircle,
  <PERSON>link,
  Copy,
  Trash2,
  Calendar,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { getConnection } from "@/lib/fetch/getConnection";
import { getAllGeneratedLinks } from "@/lib/fetch/getGeneratedLinks";
import TestConnectionButton from "@/components/dashboard/TestConnectionButton";
import CreateLinkButton from "@/components/dashboard/CreateLinkButton";
import { DateTime } from "luxon";
import { availableNetworks } from "@/constants/availableNetworks";
import ReplaceAccountButton from "@/components/dashboard/ReplaceAccountButton";
import CopyButton from "@/components/dashboard/CopyButton";
import DeleteLinkButton from "@/components/dashboard/DeleteLinkButton";
import { headers } from "next/headers";

export default async function SingleConnectionPage({
  params,
}: {
  params: Promise<{ id: string; connectionId: string }>;
}) {
  const { id: currentProjectId, connectionId } = await params;
  const connection = await getConnection(currentProjectId, connectionId);
  const oauthLinks = await getAllGeneratedLinks(currentProjectId, connectionId);

  const headersList = await headers();
  const domain = headersList.get("host") || "";

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "expired":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "default",
      error: "destructive",
      expired: "secondary",
    } as const;
    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status}
      </Badge>
    );
  };

  const ConnectionPlatformIcon =
    availableNetworks.find(
      (n) => n.slug === connection.platform_connections.platform
    )?.icon || AlertCircle;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <ConnectionPlatformIcon className="h-8 w-8" />
        <h2 className="text-2xl font-semibold tracking-tight">
          Manage {connection.platform_connections.name}
        </h2>
        {getStatusBadge(connection.platform_connections.status)}
      </div>
      {/* Connection Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Connection Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm text-muted-foreground">Status</Label>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon(connection.platform_connections.status)}
                <span className="font-medium">
                  {connection.platform_connections.status}
                </span>
              </div>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Last Sync</Label>
              <p className="font-medium mt-1">
                {connection.platform_connections.lastPolledAt}
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <TestConnectionButton
              platform={connection.platform_connections.platform}
              connectionId={connection.platform_connections.id}
              disabled={connection.platform_connections.status !== "active"}
            />
            {
              //Add Reconnect button if status is error
            }
          </div>
        </CardContent>
      </Card>

      {/* Connected Account */}

      {connection.platform_connections && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Connected Account</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <ConnectionPlatformIcon className="h-5 w-5" />
                <div>
                  <div className="font-medium">
                    {connection.platform_connections.platformAccountName}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    0 followers
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <ReplaceAccountButton connectionId={connectionId} />

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      title="Disconnect Account"
                    >
                      <Unlink className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Disconnect Account</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will disconnect "
                        {connection.platform_connections.platformAccountName}"
                        from this connection. Feeds using this connection will
                        stop receiving data.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                        Disconnect
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      {/* Generated OAuth Links */}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Generated OAuth Links</CardTitle>
              <CardDescription>
                Manage OAuth 2.0 links for this connection.
              </CardDescription>
            </div>
            <CreateLinkButton
              projectId={currentProjectId}
              connectionId={connectionId}
            />
          </div>
        </CardHeader>
        <CardContent>
          {oauthLinks.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {oauthLinks.map((link) => {
                  const isActive =
                    !link.expiresAt ||
                    DateTime.fromISO(link.expiresAt) > DateTime.now();
                  return (
                    <TableRow key={link.id}>
                      <TableCell>
                        <div className="font-medium">{link.name}</div>
                        <div className="text-xs text-muted-foreground font-mono truncate max-w-[200px]">
                          {link.token}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(isActive ? "active" : "expired")}
                          {getStatusBadge(isActive ? "active" : "expired")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{link.expiresAt}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{link.createdAt}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <CopyButton
                            title="Copy Link"
                            // TODO: Make this the public connection URL
                            value={`${domain}/dashboard/projects/${currentProjectId}/connections/${connectionId}/connect`}
                          />
                          <DeleteLinkButton
                            name={link.name}
                            linkId={link.id}
                            connectionId={connectionId}
                            projectId={currentProjectId}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No OAuth links generated for this connection yet.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Permissions */}
      {Array.isArray(connection.platform_connections.scopes) &&
        (connection.platform_connections.scopes as string[]).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Permissions</CardTitle>
              <CardDescription>
                Current permissions granted for this platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {(connection.platform_connections.scopes as string[]).map(
                  (permission) => (
                    <Badge key={permission} variant="secondary">
                      {permission}
                    </Badge>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}
    </div>
  );
}
