import type React from "react";

import { ProjectHeaderContent } from "@/components/dashboard/ProjectHeaderContent";
import { ProjectHorizontalNav } from "@/components/dashboard/ProjectHorizontalNav";
import { getProject } from "@/lib/fetch/getProject";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ChevronLeft } from "lucide-react";

export default async function ProjectDetailLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const data = await getProject({ projectId: id });

  if (!data) {
    return <ErrorState />;
  }

  const { name, id: projectId } = data;

  return (
    <div className="flex flex-col h-full pt-4">
      <ProjectHeaderContent name={name} projectId={projectId} />
      <ProjectHorizontalNav />
      <main className="flex-1 p-4 md:p-6 overflow-y-auto bg-muted/30">
        {children}
      </main>
    </div>
  );
}

const ErrorState = () => {
  return (
    <div className="flex flex-col h-full pt-4">
      <div className="flex h-16 items-center px-4 md:px-6">
        <div className="flex flex-1 items-center justify-between space-x-2 md:space-x-4">
          <div className="flex space-x-9 items-center">
            <Link href="/dashboard/projects">
              <Button variant="default" size="icon" className="rounded-full">
                <ChevronLeft className="!size-6" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
      <main className="flex-1 p-4 md:p-6 overflow-y-auto bg-muted/30">
        <div className="flex flex-col items-center justify-center h-full">
          <h2 className="text-2xl font-semibold">Project Not Found</h2>
          <p className="text-muted-foreground mt-2">
            The project you are looking for does not exist or you don't have
            access to it.
          </p>
          <Link
            href="/dashboard/projects"
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/80 mt-4"
          >
            Back to Projects
          </Link>
        </div>
      </main>
    </div>
  );
};
