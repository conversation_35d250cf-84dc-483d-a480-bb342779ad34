"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import {
  <PERSON><PERSON>,
  <PERSON>,
  EyeOff,
  Key,
  Plus,
  RefreshCw,
  Save,
  Trash2,
  Webhook,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog";
import { toast } from "@/hooks/use-toast";
import { useProjectPage } from "@/lib/contexts/project-page-context";

// Mock data
const feedData = {
  name: "Main Social Feed",
  id: "feed_001",
};

const apiKeys = [
  {
    id: "key_feed_001",
    name: "Production Feed API Key",
    key: "pk_feed_live_1234567890abcdef",
    created: "2023-10-15",
    lastUsed: "1 hour ago",
    status: "active",
    permissions: ["read"],
  },
  {
    id: "key_feed_002",
    name: "Development Feed API Key",
    key: "pk_feed_test_abcdef1234567890",
    created: "2023-11-01",
    lastUsed: "2 hours ago",
    status: "active",
    permissions: ["read"],
  },
];

const webhookEndpoints = [
  {
    id: "webhook_feed_001",
    url: "https://api.example.com/webhooks/feed-updates",
    events: ["feed.updated", "feed.error"],
    status: "active",
    lastDelivery: "10 minutes ago",
  },
];

export default function FeedAPISettingsPage() {
  const params = useParams();
  const projectId = params.id as string;
  const feedId = params.feedId as string;

  const [showApiKeys, setShowApiKeys] = useState<{ [key: string]: boolean }>(
    {}
  );
  const [isCreateKeyOpen, setIsCreateKeyOpen] = useState(false);
  const [isCreateWebhookOpen, setIsCreateWebhookOpen] = useState(false);
  const [newKeyName, setNewKeyName] = useState("");
  const [newWebhookUrl, setNewWebhookUrl] = useState("");
  const [newWebhookEvents, setNewWebhookEvents] = useState<string[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const handleSaveSettings = () => {
    setHasUnsavedChanges(false);
    toast({
      title: "Settings saved",
      description: "Feed API settings have been updated successfully.",
    });
  };

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKeys((prev) => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const handleCopyApiKey = (key: string) => {
    navigator.clipboard.writeText(key);
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard.",
    });
  };

  const handleCreateApiKey = () => {
    toast({
      title: "API key created",
      description: "New feed API key has been generated successfully.",
    });
    setIsCreateKeyOpen(false);
    setNewKeyName("");
  };

  const handleCreateWebhook = () => {
    toast({
      title: "Webhook created",
      description: "New webhook endpoint has been configured successfully.",
    });
    setIsCreateWebhookOpen(false);
    setNewWebhookUrl("");
    setNewWebhookEvents([]);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      {/* API Keys Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Feed API Keys</span>
              </CardTitle>
              <CardDescription>
                Manage API keys for accessing this feed's data.
              </CardDescription>
            </div>
            <Dialog open={isCreateKeyOpen} onOpenChange={setIsCreateKeyOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create API Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Feed API Key</DialogTitle>
                  <DialogDescription>
                    Generate a new API key for accessing this feed.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="key-name">Key Name</Label>
                    <Input
                      id="key-name"
                      placeholder="e.g., Production Feed API Key"
                      value={newKeyName}
                      onChange={(e) => setNewKeyName(e.target.value)}
                    />
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Feed API keys have read-only access to this specific feed's
                    data.
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateKeyOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleCreateApiKey} disabled={!newKeyName}>
                    Create Key
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Key</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Last Used</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apiKeys.map((apiKey) => (
                <TableRow key={apiKey.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{apiKey.name}</div>
                      <div className="text-sm text-muted-foreground">
                        Created {apiKey.created}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm font-mono">
                        {showApiKeys[apiKey.id]
                          ? apiKey.key
                          : "••••••••••••••••"}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => toggleApiKeyVisibility(apiKey.id)}
                      >
                        {showApiKeys[apiKey.id] ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleCopyApiKey(apiKey.key)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      {apiKey.permissions.map((permission) => (
                        <Badge
                          key={permission}
                          variant="outline"
                          className="text-xs"
                        >
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>{apiKey.lastUsed}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        apiKey.status === "active" ? "default" : "secondary"
                      }
                    >
                      {apiKey.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline">
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will permanently delete the API key "
                              {apiKey.name}". Applications using this key will
                              lose access immediately.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                              Delete Key
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Webhooks */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Webhook className="h-5 w-5" />
                <span>Feed Webhooks</span>
              </CardTitle>
              <CardDescription>
                Configure webhook endpoints to receive notifications about this
                feed.
              </CardDescription>
            </div>
            <Dialog
              open={isCreateWebhookOpen}
              onOpenChange={setIsCreateWebhookOpen}
            >
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Webhook
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Feed Webhook Endpoint</DialogTitle>
                  <DialogDescription>
                    Configure a new webhook endpoint to receive feed
                    notifications.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="webhook-url">Endpoint URL</Label>
                    <Input
                      id="webhook-url"
                      placeholder="https://your-app.com/webhooks/feed"
                      value={newWebhookUrl}
                      onChange={(e) => setNewWebhookUrl(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label>Events</Label>
                    <div className="space-y-2">
                      {[
                        "feed.updated",
                        "feed.error",
                        "feed.paused",
                        "feed.resumed",
                      ].map((event) => (
                        <div
                          key={event}
                          className="flex items-center space-x-2"
                        >
                          <input
                            type="checkbox"
                            id={`event-${event}`}
                            checked={newWebhookEvents.includes(event)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewWebhookEvents([
                                  ...newWebhookEvents,
                                  event,
                                ]);
                              } else {
                                setNewWebhookEvents(
                                  newWebhookEvents.filter((e) => e !== event)
                                );
                              }
                            }}
                          />
                          <Label htmlFor={`event-${event}`} className="text-sm">
                            {event}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateWebhookOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateWebhook}
                    disabled={!newWebhookUrl}
                  >
                    Add Webhook
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {webhookEndpoints.map((webhook) => (
              <div
                key={webhook.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div>
                  <div className="font-medium">{webhook.url}</div>
                  <div className="text-sm text-muted-foreground">
                    Events: {webhook.events.join(", ")}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Last delivery: {webhook.lastDelivery}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      webhook.status === "active" ? "default" : "secondary"
                    }
                  >
                    {webhook.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    Test
                  </Button>
                  <Button size="sm" variant="outline">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* API Documentation */}
      <Card>
        <CardHeader>
          <CardTitle>API Documentation</CardTitle>
          <CardDescription>
            Learn how to integrate with this feed's API.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Feed Endpoint</h4>
            <code className="text-sm">GET /api/feeds/{feedId}/posts</code>
          </div>
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Example Request</h4>
            <pre className="text-sm overflow-x-auto">
              {`curl -H "Authorization: Bearer YOUR_API_KEY" \\
     https://api.example.com/feeds/${feedId}/posts`}
            </pre>
          </div>
          <Button variant="outline">View Full Documentation</Button>
        </CardContent>
      </Card>
    </div>
  );
}
