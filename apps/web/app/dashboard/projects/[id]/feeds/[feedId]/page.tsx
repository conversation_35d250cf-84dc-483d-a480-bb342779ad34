import FeedSettingsForm from "./_components/FeedSettingsForm";
import { getFeed } from "@/lib/fetch/getFeed";
import { getConnections } from "@/lib/fetch/getConnections";

export default async function FeedConfigurationPage({
  params,
}: {
  params: Promise<{ id: string; feedId: string }>;
}) {
  const { id: projectId, feedId } = await params;

  const feedData = await getFeed(projectId, feedId);
  const connections = await getConnections(projectId);

  return (
    <FeedSettingsForm
      name={feedData.name}
      description={feedData.description ?? undefined}
      status={feedData.status}
      feedConnections={feedData.connections}
      availableConnections={connections}
      apiKeys={[]}
    />
  );
}
