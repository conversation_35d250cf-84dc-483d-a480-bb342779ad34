"use client";
import React from "react";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>riangle,
  Trash2,
  Key,
  Plus,
  Co<PERSON>,
  <PERSON>freshCw,
  EyeOff,
  Eye,
  AlertCircle,
} from "lucide-react";
import type { LucideIcon } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import StatusBadge from "@/components/primitives/StatusBadge";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { availableNetworks } from "@/constants/availableNetworks";

const FeedSettingsForm: React.FC<{
  name: string;
  description?: string;
  status: string;
  feedConnections: string[];
  availableConnections: {
    id: string;
    name: string;
    platformAccountName: string | null;
    lastPolledAt: string | null;
    projectId: string;
    platform: "instagram" | "instagram_business" | "facebook" | "tiktok";
    status: "active" | "inactive" | "auth_needed" | "expired";
    createdAt: string | null;
  }[];
  apiKeys: {
    key: string;
    keyPreview: string;
    description: string;
    status: string;
    createdBy: string;
    createdAt: number;
    updatedAt: number;
    expiresAt: number;
    lastUsedAt: number;
  }[];
}> = ({
  name,
  description,
  status,
  feedConnections,
  availableConnections,
  apiKeys,
}) => {
  const [feedName, setFeedName] = useState(name);
  const [feedDescription, setFeedDescription] = useState(description);
  const [selectedConnections, setSelectedConnections] =
    useState<string[]>(feedConnections);

  const [isActive, setIsActive] = useState(status === "active");
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const [showApiKeys, setShowApiKeys] = useState<{ [key: string]: boolean }>(
    {}
  );
  const [isCreateKeyOpen, setIsCreateKeyOpen] = useState(false);
  // const [isCreateWebhookOpen, setIsCreateWebhookOpen] = useState(false);
  const [newKeyName, setNewKeyName] = useState("");
  // const [newWebhookUrl, setNewWebhookUrl] = useState("");
  // const [newWebhookEvents, setNewWebhookEvents] = useState<string[]>([]);

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKeys((prev) => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const handleCopyApiKey = (key: string) => {
    navigator.clipboard.writeText(key);
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard.",
    });
  };

  const handleCreateApiKey = () => {
    toast({
      title: "API key created",
      description: "New feed API key has been generated successfully.",
    });
    setIsCreateKeyOpen(false);
    setNewKeyName("");
  };

  /*

  const handleCreateWebhook = () => {
    toast({
      title: "Webhook created",
      description: "New webhook endpoint has been configured successfully.",
    });
    setIsCreateWebhookOpen(false);
    setNewWebhookUrl("");
    setNewWebhookEvents([]);
  };

  */

  const handleSaveSettings = () => {
    setHasUnsavedChanges(false);
    toast({
      title: "Feed updated",
      description: "Feed configuration has been saved successfully.",
    });
  };

  const handleConnectionToggle = (connectionId: string) => {
    setSelectedConnections((prev) => {
      const newConnections = prev.includes(connectionId)
        ? prev.filter((id) => id !== connectionId)
        : [...prev, connectionId];
      setHasUnsavedChanges(true);
      return newConnections;
    });
  };

  const handleInputChange =
    (setter: (value: string) => void) => (value: string) => {
      setter(value);
      setHasUnsavedChanges(true);
    };

  const handleStatusToggle = (checked: boolean) => {
    setIsActive(checked);
    setHasUnsavedChanges(true);
  };

  console.log(
    "selectedConnections",
    selectedConnections,
    typeof selectedConnections
  );

  return (
    <div className="space-y-6">
      {" "}
      {/* Max-width can be applied here or in the layout if needed */}
      {/* Basic Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Settings</CardTitle>
          <CardDescription>
            Configure the basic properties of your feed.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="feed-name">Feed Name</Label>
            <Input
              id="feed-name"
              value={feedName}
              onChange={(e) => handleInputChange(setFeedName)(e.target.value)}
              placeholder="Enter feed name"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="feed-description">Description</Label>
            <Textarea
              id="feed-description"
              value={feedDescription}
              onChange={(e) =>
                handleInputChange(setFeedDescription)(e.target.value)
              }
              placeholder="Enter feed description"
              rows={3}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="feed-status">Feed Status</Label>
              <p className="text-sm text-muted-foreground">
                Enable or disable this feed
              </p>
            </div>
            <Switch
              id="feed-status"
              checked={isActive}
              onCheckedChange={handleStatusToggle}
            />
          </div>
        </CardContent>
      </Card>
      {/* Connection Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Data Sources</CardTitle>
          <CardDescription>
            Select which connections this feed should aggregate data from.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {availableConnections.map((connection) => {
              const IconComponent =
                availableNetworks.find((n) => n.slug === connection.platform)
                  ?.icon || AlertCircle;
              const isSelected = selectedConnections.includes(connection.id);

              const isDisabled =
                selectedConnections.length > 0 &&
                connection.platform !==
                  availableConnections.find(
                    (ac) => ac.id === selectedConnections[0]
                  )?.platform;
              return (
                <div
                  key={connection.id}
                  className={cn(
                    "flex items-center justify-between p-4 border rounded-lg",
                    isDisabled && "text-muted-foreground"
                  )}
                >
                  <div className="flex items-center space-x-4">
                    <IconComponent className="h-8 w-8" />
                    <div>
                      <h3 className="font-medium">{connection.name}</h3>
                      <p className="text-sm text-muted-foreground flex gap-2">
                        {connection.platform}
                        <span>•</span>
                        <StatusBadge
                          status={connection.status}
                          className={cn(isDisabled && "opacity-50")}
                        />
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={isSelected}
                    onCheckedChange={() =>
                      handleConnectionToggle(connection.id)
                    }
                    disabled={isDisabled}
                  />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
      {/* API Keys Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Feed API Keys</span>
              </CardTitle>
              <CardDescription>
                Manage API keys for accessing this feed's data.
              </CardDescription>
            </div>
            <Dialog open={isCreateKeyOpen} onOpenChange={setIsCreateKeyOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create API Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Feed API Key</DialogTitle>
                  <DialogDescription>
                    Generate a new API key for accessing this feed.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="key-name">Key Name</Label>
                    <Input
                      id="key-name"
                      placeholder="e.g., Production Feed API Key"
                      value={newKeyName}
                      onChange={(e) => setNewKeyName(e.target.value)}
                    />
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Feed API keys have read-only access to this specific feed's
                    data.
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateKeyOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleCreateApiKey} disabled={!newKeyName}>
                    Create Key
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Key</TableHead>
                <TableHead>Last Used</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apiKeys.map((apiKey, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{apiKey.description}</div>
                      <div className="text-sm text-muted-foreground">
                        Created {apiKey.createdAt}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm font-mono">
                        {showApiKeys[apiKey.key]
                          ? apiKey.key
                          : "••••••••••••••••"}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => toggleApiKeyVisibility(apiKey.key)}
                      >
                        {showApiKeys[apiKey.key] ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleCopyApiKey(apiKey.key)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>{apiKey.lastUsedAt}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        apiKey.status === "active" ? "default" : "secondary"
                      }
                    >
                      {apiKey.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline">
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will permanently delete the API key "
                              {apiKey.description}". Applications using this key
                              will lose access immediately.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                              Delete Key
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      {/* Webhooks */}
      {/*}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Webhook className="h-5 w-5" />
                <span>Feed Webhooks</span>
              </CardTitle>
              <CardDescription>
                Configure webhook endpoints to receive notifications about this
                feed.
              </CardDescription>
            </div>
            <Dialog
              open={isCreateWebhookOpen}
              onOpenChange={setIsCreateWebhookOpen}
            >
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Webhook
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Feed Webhook Endpoint</DialogTitle>
                  <DialogDescription>
                    Configure a new webhook endpoint to receive feed
                    notifications.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="webhook-url">Endpoint URL</Label>
                    <Input
                      id="webhook-url"
                      placeholder="https://your-app.com/webhooks/feed"
                      value={newWebhookUrl}
                      onChange={(e) => setNewWebhookUrl(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label>Events</Label>
                    <div className="space-y-2">
                      {[
                        "feed.updated",
                        "feed.error",
                        "feed.paused",
                        "feed.resumed",
                      ].map((event) => (
                        <div
                          key={event}
                          className="flex items-center space-x-2"
                        >
                          <input
                            type="checkbox"
                            id={`event-${event}`}
                            checked={newWebhookEvents.includes(event)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewWebhookEvents([
                                  ...newWebhookEvents,
                                  event,
                                ]);
                              } else {
                                setNewWebhookEvents(
                                  newWebhookEvents.filter((e) => e !== event)
                                );
                              }
                            }}
                          />
                          <Label htmlFor={`event-${event}`} className="text-sm">
                            {event}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateWebhookOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateWebhook}
                    disabled={!newWebhookUrl}
                  >
                    Add Webhook
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {webhookEndpoints.map((webhook) => (
              <div
                key={webhook.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div>
                  <div className="font-medium">{webhook.url}</div>
                  <div className="text-sm text-muted-foreground">
                    Events: {webhook.events.join(", ")}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Last delivery: {webhook.lastDelivery}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      webhook.status === "active" ? "default" : "secondary"
                    }
                  >
                    {webhook.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    Test
                  </Button>
                  <Button size="sm" variant="outline">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      {*/}
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Danger Zone</span>
          </CardTitle>
          <CardDescription>
            Irreversible and destructive actions for this project.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 border border-destructive rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-destructive">Delete Project</h3>
                <p className="text-sm text-muted-foreground">
                  Permanently delete this project and all associated data. This
                  action cannot be undone.
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Project
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete
                      the project "AAA" and remove all associated data.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => console.log("DELETE")}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Yes, delete project
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Feed Statistics */}
      {/*}
      <Card>
        <CardHeader>
          <CardTitle>Feed Statistics</CardTitle>
          <CardDescription>Current statistics for this feed.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {feedData.postsCount.toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Total Posts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {selectedConnections.length}
              </div>
              <div className="text-sm text-muted-foreground">
                Connected Sources
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{activeFilters.length}</div>
              <div className="text-sm text-muted-foreground">
                Active Filters
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {isActive ? "Active" : "Paused"}
              </div>
              <div className="text-sm text-muted-foreground">Status</div>
            </div>
          </div>
        </CardContent>
      </Card>
      {*/}
    </div>
  );
};

export default FeedSettingsForm;
