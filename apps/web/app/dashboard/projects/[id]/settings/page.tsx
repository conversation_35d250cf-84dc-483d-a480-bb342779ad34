"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, Trash2, Al<PERSON><PERSON>riangle } from "lucide-react";
import { useProjectPage } from "@/lib/contexts/project-page-context";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "@/hooks/use-toast";

// Mock data
const projectSettingsMockData = {
  name: "PLAZE Feed",
  description:
    "Social media analytics and data collection for e-commerce insights",
};

export default function ProjectSettingsPage() {
  const [projectName, setProjectName] = useState(projectSettingsMockData.name);
  const [projectDescription, setProjectDescription] = useState(
    projectSettingsMockData.description
  );
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const handleProjectNameChange = (value: string) => {
    setProjectName(value);
    setHasUnsavedChanges(true);
  };

  const handleProjectDescriptionChange = (value: string) => {
    setProjectDescription(value);
    setHasUnsavedChanges(true);
  };

  const handleSaveSettings = () => {
    setHasUnsavedChanges(false);
    toast({
      title: "Settings Saved",
      description: "Project settings have been updated successfully.",
    });
    // In a real app, you would persist these changes
  };

  const handleDeleteProject = () => {
    toast({
      title: "Project Deleted",
      description: "The project has been permanently deleted.",
      variant: "destructive",
    });
    // In a real app, navigate away or update UI
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Project Information</CardTitle>
          <CardDescription>
            Update your project name and description.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="project-name">Project Name</Label>
            <Input
              id="project-name"
              value={projectName}
              onChange={(e) => handleProjectNameChange(e.target.value)}
              placeholder="Enter project name"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="project-description">Description</Label>
            <Textarea
              id="project-description"
              value={projectDescription}
              onChange={(e) => handleProjectDescriptionChange(e.target.value)}
              placeholder="Enter project description"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Danger Zone</span>
          </CardTitle>
          <CardDescription>
            Irreversible and destructive actions for this project.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 border border-destructive rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-destructive">Delete Project</h3>
                <p className="text-sm text-muted-foreground">
                  Permanently delete this project and all associated data. This
                  action cannot be undone.
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Project
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete
                      the project "{projectName}" and remove all associated
                      data.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteProject}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Yes, delete project
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
