"use client"

import { Label } from "@/components/ui/label"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import {
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  CheckCircle,
  Clock,
  AlertTriangle,
  ExternalLink,
  Shield,
  Users,
  Zap,
  Info,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { toast } from "@/hooks/use-toast"
import Link from "next/link"

// Mock data - in real app, this would be fetched based on the token
const linkData = {
  id: "link_001",
  token: "abc123def",
  name: "Client Demo Access",
  projectName: "PLAZE Feed",
  projectDescription: "Social media analytics and data collection for e-commerce insights",
  companyName: "Acme Corporation",
  expiresAt: "2026-01-15",
  status: "active",
  isValid: true,
}

const availableNetworks = [
  {
    name: "Facebook",
    slug: "facebook",
    icon: Facebook,
    enabled: true,
    connected: false,
    color: "bg-blue-600 hover:bg-blue-700",
    description: "Connect your Facebook pages and posts",
    permissions: ["Read page insights", "Access post data", "View page information"],
  },
  {
    name: "Instagram",
    slug: "instagram",
    icon: Instagram,
    enabled: true,
    connected: true,
    color: "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",
    description: "Connect your Instagram account (Business or Personal)",
    permissions: ["Read post insights", "Access story data", "View profile information"],
    accountTypes: [
      { id: "business", name: "Business/Creator", fullAccess: true },
      { id: "personal", name: "Personal/Private", fullAccess: false },
    ],
    note: "Private accounts have limited data access",
  },
  {
    name: "Twitter",
    slug: "twitter",
    icon: Twitter,
    enabled: false,
    connected: false,
    color: "bg-sky-500 hover:bg-sky-600",
    description: "Connect your Twitter account",
    permissions: ["Read tweets", "Access mentions", "View profile data"],
  },
  {
    name: "YouTube",
    slug: "youtube",
    icon: Youtube,
    enabled: true,
    connected: false,
    color: "bg-red-600 hover:bg-red-700",
    description: "Connect your YouTube channel",
    permissions: ["Read video analytics", "Access channel data", "View subscriber information"],
  },
]

export default function ConnectionLinkPage() {
  const params = useParams()
  const token = params.token as string
  const [connectedNetworks, setConnectedNetworks] = useState<string[]>(
    availableNetworks.filter((n) => n.connected).map((n) => n.slug),
  )
  const [isLoading, setIsLoading] = useState(false)

  // Calculate progress
  const enabledNetworks = availableNetworks.filter((n) => n.enabled)
  const progress = (connectedNetworks.length / enabledNetworks.length) * 100

  useEffect(() => {
    // In real app, validate token and fetch link data
    if (!linkData.isValid) {
      toast({
        title: "Invalid Link",
        description: "This connection link is invalid or has expired.",
        variant: "destructive",
      })
    }
  }, [token])

  const handleConnect = async (networkSlug: string) => {
    setIsLoading(true)
    // Mock connection delay
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Mock successful connection
    setConnectedNetworks((prev) => [...prev, networkSlug])
    setIsLoading(false)

    toast({
      title: "Connection successful",
      description: `Your ${availableNetworks.find((n) => n.slug === networkSlug)?.name} account has been connected.`,
    })
  }

  const handleDisconnect = async (networkSlug: string) => {
    setConnectedNetworks((prev) => prev.filter((slug) => slug !== networkSlug))

    toast({
      title: "Account disconnected",
      description: `Your ${availableNetworks.find((n) => n.slug === networkSlug)?.name} account has been disconnected.`,
    })
  }

  const isExpired = new Date(linkData.expiresAt) < new Date()

  if (!linkData.isValid || isExpired) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Connection Link Unavailable</h3>
                <p className="text-muted-foreground mt-2">
                  {isExpired && "This connection link has expired."}
                  {!linkData.isValid && "This connection link is invalid."}
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                Please contact {linkData.companyName} for a new connection link.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-white">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">{linkData.projectName}</h1>
                <p className="text-sm text-muted-foreground">by {linkData.companyName}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Welcome Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Connect Your Social Media Accounts</span>
            </CardTitle>
            <CardDescription>{linkData.projectDescription}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Connection Progress</p>
                <p className="text-sm text-muted-foreground">
                  {connectedNetworks.length} of {enabledNetworks.length} accounts connected
                </p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold">{Math.round(progress)}%</p>
                <p className="text-sm text-muted-foreground">Complete</p>
              </div>
            </div>
            <Progress value={progress} className="w-full" />

            {progress === 100 && (
              <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">All accounts connected successfully!</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Social Networks */}
        <div className="grid gap-4 md:grid-cols-2">
          {availableNetworks
            .filter((network) => network.enabled)
            .map((network) => {
              const IconComponent = network.icon
              const isConnected = connectedNetworks.includes(network.slug)

              return (
                <Card key={network.name} className={isConnected ? "border-green-200 bg-green-50" : ""}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-lg ${network.color} flex items-center justify-center`}>
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold flex items-center space-x-2">
                            <span>{network.name}</span>
                            {isConnected && <CheckCircle className="h-4 w-4 text-green-600" />}
                          </h3>
                          <p className="text-sm text-muted-foreground">{network.description}</p>
                        </div>
                      </div>
                      <div className="flex flex-col space-y-2">
                        {isConnected ? (
                          <>
                            <Badge variant="default" className="bg-green-600">
                              Connected
                            </Badge>
                            <Button size="sm" variant="outline" onClick={() => handleDisconnect(network.slug)}>
                              Disconnect
                            </Button>
                          </>
                        ) : (
                          <Link href={`/connect/${network.slug}?token=${token}`}>
                            <Button size="sm" disabled={isLoading} className={network.color}>
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Connect
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>

                    {/* Permissions */}
                    <div className="mt-4 pt-4 border-t">
                      <p className="text-sm font-medium mb-2">This will allow access to:</p>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {network.permissions.map((permission, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full" />
                            <span>{permission}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    {/* Add this after the permissions list for Instagram */}
                    {network.slug === "instagram" && network.note && (
                      <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded text-xs text-orange-700">
                        <Info className="h-3 w-3 inline mr-1" />
                        {network.note}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
        </div>

        {/* Link Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Connection Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label className="text-sm text-muted-foreground">Link Name</Label>
                <p className="font-medium">{linkData.name}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Expires On</Label>
                <p className="font-medium">{new Date(linkData.expiresAt).toLocaleDateString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Notice */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="space-y-1">
                <p className="font-medium text-blue-900">Your data is secure</p>
                <p className="text-sm text-blue-700">
                  We use industry-standard encryption and security practices to protect your social media data. You can
                  revoke access at any time through your social media account settings.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground pt-6 border-t">
          <p>
            Having trouble connecting? Contact{" "}
            <a
              href={`mailto:support@${linkData.companyName.toLowerCase().replace(/\s+/g, "")}.com`}
              className="text-primary hover:underline"
            >
              {linkData.companyName} Support
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
